# حماية نظام الاستبيان JST
# JST Survey System Protection

# منع الوصول للملفات الحساسة
<Files "config.php">
    Order Allow,Deny
    Deny from all
</Files>

<Files "database.sql">
    Order Allow,Deny
    Deny from all
</Files>

<Files "install.php">
    Order Allow,Deny
    Deny from all
</Files>

# حماية مجلدات النظام
<Directory "includes">
    Order Allow,Deny
    Deny from all
</Directory>

<Directory "logs">
    Order Allow,Deny
    Deny from all
</Directory>

<Directory "backups">
    Order Allow,Deny
    Deny from all
</Directory>

# منع عرض محتويات المجلدات
Options -Indexes

# حماية من هجمات XSS
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data:; connect-src 'self';"
</IfModule>

# إعدادات PHP للأمان
<IfModule mod_php7.c>
    php_flag display_errors Off
    php_flag log_errors On
    php_flag expose_php Off
    php_value session.cookie_httponly 1
    php_value session.cookie_secure 1
    php_value session.use_only_cookies 1
</IfModule>

# منع الوصول للملفات المخفية
<Files ".*">
    Order Allow,Deny
    Deny from all
</Files>

# حماية من هجمات الحقن
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # منع الوصول للملفات الحساسة
    RewriteRule ^(config|database|install)\.php$ - [F,L]
    RewriteRule ^(includes|logs|backups)/ - [F,L]
    
    # منع محاولات الحقن الشائعة
    RewriteCond %{QUERY_STRING} (\<|%3C).*script.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} proc/self/environ [OR]
    RewriteCond %{QUERY_STRING} mosConfig_[a-zA-Z_]{1,21}(=|\%3D) [OR]
    RewriteCond %{QUERY_STRING} base64_(en|de)code[^(]*\([^)]*\) [OR]
    RewriteCond %{QUERY_STRING} (<|%3C)([^s]*s)+cript.*(>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*iframe.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (<|%3C)([^e]*e)+mbed.*(>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (<|%3C)([^o]*o)+bject.*(>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (<|%3C)([^a]*a)+pplet.*(>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} \b(and|or)\b.{1,6}?(=|>|<|\bin\b|\blike\b) [NC,OR]
    RewriteCond %{QUERY_STRING} \b(and|or)\b\s.{1,4}?(=|>|<|\bin\b|\blike\b) [NC,OR]
    RewriteCond %{QUERY_STRING} \b(select|union|insert|drop|delete|update|cast|create|char|convert|alter|declare|exec|or|concat)\b [NC,OR]
    RewriteCond %{QUERY_STRING} \b(sp_executesql)\b [NC]
    RewriteRule ^(.*)$ - [F,L]
</IfModule>

# ضغط الملفات لتحسين الأداء
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# تخزين مؤقت للملفات الثابتة
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/ico "access plus 1 month"
    ExpiresByType image/icon "access plus 1 month"
    ExpiresByType text/x-icon "access plus 1 month"
    ExpiresByType application/x-icon "access plus 1 month"
    ExpiresByType font/woff "access plus 1 month"
    ExpiresByType font/woff2 "access plus 1 month"
</IfModule>

# حد أقصى لحجم الملفات المرفوعة
<IfModule mod_php7.c>
    php_value upload_max_filesize 5M
    php_value post_max_size 5M
    php_value max_execution_time 30
    php_value max_input_time 30
    php_value memory_limit 128M
</IfModule>

# منع الوصول من User Agents المشبوهة
<IfModule mod_rewrite.c>
    RewriteCond %{HTTP_USER_AGENT} ^$ [OR]
    RewriteCond %{HTTP_USER_AGENT} ^(java|curl|wget) [NC,OR]
    RewriteCond %{HTTP_USER_AGENT} (winhttp|HTTrack|clshttp|archiver|loader|email|harvest|extract|grab|miner) [NC,OR]
    RewriteCond %{HTTP_USER_AGENT} (libwww-perl|curl|wget|python|nikto|scan) [NC,OR]
    RewriteCond %{HTTP_USER_AGENT} (<|>|'|%0A|%0D|%27|%3C|%3E|%00) [NC,OR]
    RewriteCond %{HTTP_USER_AGENT} (;|<|>|'|"|\)|\(|%0A|%0D|%22|%27|%28|%3C|%3E|%00).*(libwww-perl|wget|python|nikto|curl|scan|java|winhttp|HTTrack|clshttp|archiver|loader|email|harvest|extract|grab|miner) [NC]
    RewriteRule .* - [F,L]
</IfModule>

# منع الهوت لينكينغ للصور
<IfModule mod_rewrite.c>
    RewriteCond %{HTTP_REFERER} !^$
    RewriteCond %{HTTP_REFERER} !^http(s)?://(www\.)?localhost [NC]
    RewriteCond %{HTTP_REFERER} !^http(s)?://(www\.)?127.0.0.1 [NC]
    RewriteRule \.(jpg|jpeg|png|gif|bmp)$ - [F,L]
</IfModule>
