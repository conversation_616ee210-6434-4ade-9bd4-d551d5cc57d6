# حماية نظام الاستبيان JST
# JST Survey System Protection

# منع الوصول للملفات الحساسة
<Files "database.sql">
    Require all denied
</Files>

<Files "config.php">
    Require all denied
</Files>

<Files "install.php">
    Require all denied
</Files>

# حماية مجلدات النظام
<DirectoryMatch "^.*(logs|backups|includes).*$">
    Require all denied
</DirectoryMatch>

# منع عرض محتويات المجلدات
Options -Indexes

# حماية أساسية من XSS
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options SAMEORIGIN
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>

# إعدادات PHP للأمان والأداء
<IfModule mod_php.c>
    php_value upload_max_filesize 5M
    php_value post_max_size 5M
    php_value max_execution_time 30
    php_value memory_limit 128M
    php_flag expose_php Off
    php_value session.cookie_httponly 1
    php_value session.use_only_cookies 1
</IfModule>

# منع الوصول للملفات المخفية
<FilesMatch "^\.">
    Require all denied
</FilesMatch>

# حماية أساسية من الحقن
<IfModule mod_rewrite.c>
    RewriteEngine On

    # منع الوصول للملفات الحساسة
    RewriteRule ^(database|install)\.php$ - [F,L]
    RewriteRule ^(logs|backups|includes)/ - [F,L]

    # منع بعض محاولات الحقن الأساسية
    RewriteCond %{QUERY_STRING} (\<|%3C).*script.*(\>|%3E) [NC]
    RewriteRule ^(.*)$ - [F,L]
</IfModule>

# ضغط الملفات لتحسين الأداء
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/html text/css text/javascript application/javascript
</IfModule>


