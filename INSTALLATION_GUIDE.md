# دليل التثبيت الكامل - نظام استبيان JST
## Complete Installation Guide - JST Survey System

## 🎯 **الهدف من ملف .htaccess**

ملف `.htaccess` مهم جداً للحماية ويوفر:

### 🔒 **الحماية الأمنية:**
- منع الوصول للملفات الحساسة (config.php, database.sql)
- حماية المجلدات الحساسة (logs, backups, includes)
- منع الوصول للملفات المخفية (.env, .git)
- حماية من هجمات XSS الأساسية

### ⚡ **تحسين الأداء:**
- ضغط الملفات (HTML, CSS, JS)
- إعدادات PHP محسنة
- منع عرض محتويات المجلدات

### 🛡️ **الحماية من الهجمات:**
- حماية الجلسات (Session Security)
- إخفاء معلومات PHP
- حماية الكوكيز

## 🚀 **خطوات التثبيت الصحيحة**

### 1. التحضير
```bash
# تأكد من تشغيل XAMPP/WAMP
# تأكد من عمل Apache و MySQL
```

### 2. رفع الملفات
```bash
# انسخ جميع ملفات المشروع إلى:
C:\xampp\htdocs\dhsurvey\
```

### 3. إصلاح قاعدة البيانات
```
افتح: http://localhost/dhsurvey/fix_database.php
سيقوم بحذف وإعادة إنشاء قاعدة البيانات بشكل صحيح
```

### 4. التحقق من النظام
```
افتح: http://localhost/dhsurvey/test_system.php
تأكد من نجاح جميع الاختبارات
```

### 5. تسجيل الدخول
```
الرابط: http://localhost/dhsurvey/admin/login.php
استخدم بيانات المستخدم المُنشأة أثناء التثبيت
```

## 🔧 **حل المشاكل الشائعة**

### Internal Server Error
```
السبب: ملف .htaccess معقد أو خاطئ
الحل: 
1. احذف ملف .htaccess مؤقتاً
2. جرب الوصول للصفحة
3. إذا عملت، أعد إنشاء .htaccess بسيط
```

### Table doesn't exist
```
السبب: قاعدة البيانات لم يتم إنشاؤها بشكل صحيح
الحل: شغل fix_database.php
```

### Access Denied
```
السبب: ملف .htaccess يمنع الوصول
الحل: تحقق من قواعد .htaccess
```

## 📋 **قائمة التحقق النهائية**

- [ ] XAMPP/WAMP يعمل
- [ ] Apache يعمل
- [ ] MySQL يعمل
- [ ] تم رفع جميع الملفات
- [ ] تم تشغيل fix_database.php
- [ ] جميع الجداول موجودة
- [ ] test_system.php يظهر نجاح 90%+
- [ ] يمكن الدخول للوحة الإدارة
- [ ] الاستبيان يعمل
- [ ] ملف .htaccess موجود للحماية

## 🔗 **الروابط المهمة**

| الوظيفة | الرابط |
|---------|--------|
| إصلاح قاعدة البيانات | `/fix_database.php` |
| اختبار النظام | `/test_system.php` |
| لوحة الإدارة | `/admin/login.php` |
| الاستبيان | `/index.php?inv=TEST123&le=01&br=0101&sm=ID0003&cp=0501234567` |

## 🎯 **نصائح مهمة**

### للحماية:
- احتفظ بملف .htaccess دائماً
- لا تحذف مجلد includes
- غير كلمة مرور Admin بعد التثبيت

### للأداء:
- نظف مجلد logs دورياً
- اعمل نسخ احتياطية من قاعدة البيانات
- راقب حجم مجلد uploads

### للصيانة:
- تحقق من test_system.php دورياً
- راجع سجل الأخطاء في logs/
- حدث كلمات المرور دورياً

## 📞 **الدعم الفني**

إذا واجهت مشاكل:
1. شغل `test_system.php` أولاً
2. تحقق من ملف `logs/error_*.log`
3. تأكد من إعدادات `config.php`
4. جرب `fix_database.php` إذا كانت المشكلة في قاعدة البيانات

---
**تذكر**: ملف .htaccess مهم للحماية، لكن يجب أن يكون بسيط ومتوافق مع إعدادات الخادم!
