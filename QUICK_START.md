# دليل البدء السريع - نظام استبيان JST
## Quick Start Guide - JST Survey System

## 🚀 البدء السريع

### 1. التثبيت
```
1. تأكد من تشغيل XAMPP/WAMP
2. افتح: http://localhost/dhsurvey/setup.php
3. انقر "بدء التثبيت"
4. انتظر حتى اكتمال التثبيت
```

### 2. تسجيل الدخول
```
الرابط: http://localhost/dhsurvey/admin/login.php
استخدم بيانات المستخدم المُنشأة أثناء التثبيت
```

### 3. اختبار الاستبيان
```
الرابط: http://localhost/dhsurvey/index.php?inv=TEST123&le=01&br=0101&sm=ID0003&cp=0501234567
```

### 4. اختبار النظام
```
الرابط: http://localhost/dhsurvey/test_system.php
```

## 📋 الصفحات الرئيسية

| الصفحة | الرابط | الوصف |
|--------|--------|-------|
| التثبيت | `/setup.php` | تثبيت قاعدة البيانات |
| لوحة الإدارة | `/admin/` | إدارة النظام |
| الاستبيان | `/index.php` | واجهة الاستبيان |
| اختبار النظام | `/test_system.php` | فحص سلامة النظام |

## 🔧 حل المشاكل الشائعة

### Internal Server Error
```
1. احذف ملف .htaccess مؤقتاً
2. جرب الوصول للصفحة
3. إذا عملت، المشكلة في .htaccess
4. أعد إنشاء .htaccess بسيط
```

### خطأ قاعدة البيانات
```
1. تأكد من تشغيل MySQL
2. تحقق من إعدادات config.php
3. أعد تشغيل setup.php
```

### صفحة فارغة
```
1. تحقق من سجل أخطاء PHP
2. تأكد من وجود جميع الملفات
3. تحقق من صلاحيات المجلدات
```

## 📞 الدعم السريع

### ملفات مهمة للفحص:
- `config.php` - إعدادات قاعدة البيانات
- `.htaccess` - إعدادات الحماية
- `logs/` - سجلات الأخطاء
- `database.sql` - هيكل قاعدة البيانات

### أوامر مفيدة:
```bash
# فحص حالة Apache
httpd -t

# فحص حالة MySQL
mysql -u root -p

# فحص أخطاء PHP
tail -f /path/to/php/error.log
```

## ✅ قائمة التحقق

- [ ] XAMPP/WAMP يعمل
- [ ] MySQL يعمل
- [ ] تم تشغيل setup.php
- [ ] تم إنشاء قاعدة البيانات
- [ ] يمكن الدخول للوحة الإدارة
- [ ] الاستبيان يعمل
- [ ] اختبار النظام ناجح

---
**نصيحة**: احتفظ بنسخة احتياطية من قاعدة البيانات دورياً!
