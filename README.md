# نظام استبيان JST للعملاء
## JST Customer Survey System

نظام شامل لإدارة استبيانات رضا العملاء بعد الشراء مع لوحة إدارة متقدمة ونظام أمان محكم.

## 🌟 الميزات الرئيسية

### واجهة الاستبيان
- ✅ تصميم متجاوب يعمل على جميع الأجهزة
- ✅ نظام تقييم بالنجوم تفاعلي
- ✅ أسئلة نصية للملاحظات
- ✅ تصميم جميل وسهل الاستخدام
- ✅ دعم كامل للغة العربية

### لوحة الإدارة
- 📊 **لوحة المعلومات**: إحصائيات شاملة ورسوم بيانية تفاعلية
- 📈 **التقارير**: فلاتر متقدمة وتصدير إلى Excel
- ⚙️ **إعدادات الاستبيان**: إدارة الأسئلة والإعدادات العامة
- 👥 **إدارة المستخدمين**: نظام صلاحيات متدرج
- 🏢 **الإعدادات العامة**: إدارة الشركات والفروع والمندوبين
- 📋 **سجل العمليات**: تتبع جميع العمليات في النظام
- 💾 **النسخ الاحتياطي**: إنشاء واستعادة النسخ الاحتياطية

### الأمان والحماية
- 🔐 تشفير كلمات المرور باستخدام bcrypt
- 🛡️ حماية من هجمات SQL Injection
- 🔒 جلسات آمنة مع انتهاء صلاحية
- 🚫 حماية من هجمات XSS و CSRF
- 📝 تسجيل جميع العمليات
- 🔄 نظام النسخ الاحتياطي والاستعادة
- 🚨 حظر IP للمحاولات المشبوهة

## 📋 متطلبات النظام

- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- Apache/Nginx مع mod_rewrite
- امتدادات PHP: PDO, OpenSSL, JSON

## 🚀 التثبيت

### 1. تحضير البيئة
```bash
# تأكد من تشغيل XAMPP أو WAMP
# إنشاء مجلد المشروع في htdocs
cd C:\xampp\htdocs\
mkdir dhsurvey
```

### 2. رفع الملفات
- انسخ جميع ملفات المشروع إلى مجلد `dhsurvey`
- تأكد من وجود الصلاحيات المناسبة للمجلدات

### 3. إعداد قاعدة البيانات
1. افتح المتصفح وانتقل إلى: `http://localhost/dhsurvey/setup.php`
2. انقر على "بدء التثبيت"
3. سيتم إنشاء قاعدة البيانات وإدراج البيانات الافتراضية

### 4. تسجيل الدخول
- استخدم بيانات المستخدم التي تم إنشاؤها أثناء التثبيت

## 🔗 الروابط المهمة

- **لوحة الإدارة**: `http://localhost/dhsurvey/admin/`
- **رابط الاستبيان التجريبي**:
  `http://localhost/dhsurvey/index.php?inv=TEST123&le=01&br=0101&sm=ID0003&cp=0501234567`

## 👥 نظام الصلاحيات

### مدير عام (Super Admin)
- صلاحيات كاملة في النظام
- إدارة المستخدمين والشركات
- الوصول لجميع التقارير والإعدادات
- النسخ الاحتياطي وسجل العمليات

### مدير (Manager)
- إدارة الاستبيانات والتقارير
- عرض الإحصائيات والتحليلات
- تصدير التقارير
- لا يمكنه إدارة المستخدمين

### مستخدم (User)
- الاطلاع على التقارير فقط
- تصدير البيانات إلى Excel
- لا يمكنه التعديل

## 🎨 التقنيات المستخدمة

- **Backend**: PHP 8.2, MySQL
- **Frontend**: HTML5, CSS3, JavaScript
- **Framework CSS**: Bootstrap 5.1.3
- **الرسوم البيانية**: Chart.js
- **الأيقونات**: Font Awesome 6.0
- **الخطوط**: Google Fonts (Cairo)

## 📖 دليل الاستخدام

### إنشاء رابط الاستبيان
```
http://localhost/dhsurvey/?inv=رقم_الفاتورة&le=كود_الشركة&br=كود_الفرع&sm=كود_المندوب&cp=رقم_العميل
```

**مثال:**
```
http://localhost/dhsurvey/?inv=INV001&le=01&br=0101&sm=ID0003&cp=0501234567
```

### الوصول للوحة الإدارة
```
http://localhost/dhsurvey/admin/
```

## 👥 نظام الصلاحيات

### مدير عام (Super Admin)
- صلاحيات كاملة في النظام
- إدارة المستخدمين والصلاحيات
- الإعدادات العامة والنسخ الاحتياطي
- لا يمكن حذفه أو تعديل صلاحياته

### مدير (Manager)
- صلاحيات كاملة عدا إدارة المستخدمين
- إدارة الاستبيانات والتقارير
- عرض السجلات

### مستخدم (User)
- الاطلاع على التقارير فقط
- تصدير النتائج إلى Excel

## 🗂️ هيكل المشروع

```
dhsurvey/
├── admin/                  # لوحة الإدارة
│   ├── includes/          # ملفات مشتركة للإدارة
│   ├── index.php          # لوحة المعلومات
│   ├── login.php          # تسجيل الدخول
│   ├── reports.php        # التقارير
│   ├── survey_settings.php # إعدادات الاستبيان
│   ├── users.php          # إدارة المستخدمين
│   ├── general_settings.php # الإعدادات العامة
│   ├── logs.php           # سجل العمليات
│   └── backup.php         # النسخ الاحتياطي
├── includes/              # ملفات النظام
│   └── security.php       # وظائف الأمان
├── assets/               # الملفات الثابتة
├── css/                  # ملفات التنسيق
├── js/                   # ملفات JavaScript
├── uploads/              # الملفات المرفوعة
├── backups/              # النسخ الاحتياطية
├── logs/                 # ملفات السجلات
├── config.php            # إعدادات قاعدة البيانات
├── index.php             # الصفحة الرئيسية للاستبيان
├── database.sql          # هيكل قاعدة البيانات
├── install.php           # معالج التثبيت
└── .htaccess            # إعدادات الأمان
```

## 🔧 الإعدادات المتقدمة

### تخصيص الأسئلة
1. انتقل إلى **إعدادات الاستبيان** في لوحة الإدارة
2. يمكنك تعديل نص الأسئلة
3. إظهار/إخفاء الأسئلة
4. تحديد الأسئلة المطلوبة
5. ترتيب الأسئلة

### إدارة الشركات والفروع
1. انتقل إلى **الإعدادات العامة**
2. أضف الشركات مع أكوادها
3. أضف الفروع وربطها بالشركات
4. أضف المندوبين مع أكوادهم

### النسخ الاحتياطي
1. انتقل إلى **النسخ الاحتياطي**
2. انقر على **إنشاء نسخة احتياطية**
3. يمكنك تحميل النسخ أو استعادتها

## 📊 البيانات الافتراضية

### الشركات
- **01**: شركة أنظمة جدة للتجارة
- **02**: شركة حلم المنزل للأثاث

### الأسئلة الافتراضية
1. وش رأيك بمنتجاتنا؟ ✨ (تقييم)
2. جازت لك خدمة الموظف وما قصر معك؟ 👩‍💻👨🏻‍💻 (تقييم)
3. تنصح وتوصي الاهل والاصدقاء بـ حلم المنزل؟ 🗣 (تقييم)
4. لو باقي شي بالخاطر أو ودك تقوله لنا، هذي مساحه فاضيه خذ فيها راحتك 🔴 (نص)

## 🛠️ استكشاف الأخطاء

### مشاكل شائعة

**خطأ في الاتصال بقاعدة البيانات:**
- تأكد من تشغيل MySQL
- تحقق من بيانات الاتصال في `config.php`

**صفحة فارغة أو خطأ 500:**
- تحقق من ملفات السجلات في مجلد `logs`
- تأكد من صلاحيات المجلدات

**لا يمكن الوصول للوحة الإدارة:**
- تأكد من إنشاء المستخدم الافتراضي
- جرب إعادة تشغيل معالج التثبيت

## 🔒 الأمان

### إرشادات الأمان
1. غيّر كلمة مرور المدير الافتراضية فوراً
2. استخدم HTTPS في البيئة الإنتاجية
3. قم بعمل نسخ احتياطية دورية
4. راقب سجل العمليات بانتظام
5. حدّث النظام عند توفر تحديثات

### ملفات محمية
- `config.php` - إعدادات قاعدة البيانات
- `database.sql` - هيكل قاعدة البيانات
- `includes/` - ملفات النظام
- `logs/` - ملفات السجلات
- `backups/` - النسخ الاحتياطية

## 📞 الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل:
- تحقق من ملفات السجلات أولاً
- راجع هذا الدليل للحلول الشائعة
- تأكد من استيفاء متطلبات النظام

## 📄 الترخيص

هذا المشروع مطور خصيصاً لنظام استبيان JST.

---

**تم التطوير بواسطة:** فريق تطوير JST  
**الإصدار:** 1.0.0  
**تاريخ الإصدار:** 2025-06-19
