<?php
/**
 * إضافة البيانات الافتراضية
 * Add Default Data
 */

require_once 'config.php';

$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['add_data'])) {
    try {
        $pdo->beginTransaction();
        
        // حذف البيانات الموجودة (إذا كانت موجودة)
        $pdo->exec("DELETE FROM survey_answers");
        $pdo->exec("DELETE FROM survey_responses");
        $pdo->exec("DELETE FROM sales_representatives");
        $pdo->exec("DELETE FROM branches");
        $pdo->exec("DELETE FROM companies WHERE code IN ('01', '02')");
        
        // إضافة الشركات
        $companies = [
            ['01', 'شركة أنظمة جدة للتجارة'],
            ['02', 'شركة حلم المنزل للأثاث']
        ];
        
        $stmt = $pdo->prepare("INSERT INTO companies (code, name, is_active) VALUES (?, ?, 1)");
        foreach ($companies as $company) {
            $stmt->execute($company);
        }
        
        // الحصول على معرفات الشركات
        $stmt = $pdo->prepare("SELECT id, code FROM companies WHERE code IN ('01', '02')");
        $stmt->execute();
        $company_ids = [];
        while ($row = $stmt->fetch()) {
            $company_ids[$row['code']] = $row['id'];
        }
        
        // إضافة الفروع
        $branches = [
            ['0109', 'المتجر انظمة', $company_ids['01']],
            ['0101', 'فرع العليا انظمة', $company_ids['01']],
            ['0105', 'مشاريع انظمة', $company_ids['01']],
            ['0201', 'فرع العليا حلم', $company_ids['02']],
            ['0209', 'المتجر حلم', $company_ids['02']],
            ['0203', 'فرع صحاري مول، بوابه ٦', $company_ids['02']],
            ['0205', 'مشاريع حلم', $company_ids['02']]
        ];
        
        $stmt = $pdo->prepare("INSERT INTO branches (code, name, company_id, is_active) VALUES (?, ?, ?, 1)");
        foreach ($branches as $branch) {
            $stmt->execute($branch);
        }
        
        // إضافة مندوبي المبيعات
        $sales_reps = [
            ['ID0003', 'هاني سالم'],
            ['ID0012', 'خالد سالم'],
            ['ID0030', 'عبدالله صلاح'],
            ['ID0141', 'مصطفى عبدالله'],
            ['ID0041', 'عبد المجيد محمد'],
            ['ID0025', 'عيسى سالم'],
            ['ID0105', 'ياسر فرحان'],
            ['ID0122', 'سعد محمد'],
            ['ID0139', 'زيفه ناصر'],
            ['ID0142', 'شهد محمد'],
            ['ID0143', 'عبد الرحمن صالح'],
            ['ID0145', 'شريفه حسين'],
            ['ID0146', 'مها عمر'],
            ['ID0147', 'دعاء حسان'],
            ['ID0148', 'ناديه جمال'],
            ['ID0011', 'مرعي عمر'],
            ['ID0022', 'روان الدوسري'],
            ['ID0028', 'محمد الشاطري'],
            ['ID0014', 'عزيز محمود']
        ];
        
        $stmt = $pdo->prepare("INSERT INTO sales_representatives (code, name, is_active) VALUES (?, ?, 1)");
        foreach ($sales_reps as $rep) {
            $stmt->execute($rep);
        }
        
        $pdo->commit();
        $success_message = 'تم إضافة جميع البيانات الافتراضية بنجاح!';
        
    } catch (Exception $e) {
        $pdo->rollBack();
        $error_message = 'حدث خطأ: ' . $e->getMessage();
    }
}

// جلب الإحصائيات الحالية
$stats = [];
$stmt = $pdo->query("SELECT COUNT(*) as count FROM companies WHERE is_active = 1");
$stats['companies'] = $stmt->fetchColumn();

$stmt = $pdo->query("SELECT COUNT(*) as count FROM branches WHERE is_active = 1");
$stats['branches'] = $stmt->fetchColumn();

$stmt = $pdo->query("SELECT COUNT(*) as count FROM sales_representatives WHERE is_active = 1");
$stats['sales_reps'] = $stmt->fetchColumn();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة البيانات الافتراضية - نظام الاستبيان</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        
        .container {
            max-width: 800px;
        }
        
        .card {
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            border: none;
        }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            padding: 20px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
        
        .stats-card {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            margin-bottom: 15px;
        }
        
        .data-preview {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <div class="card-header text-center">
                <h2><i class="fas fa-database me-3"></i>إضافة البيانات الافتراضية</h2>
                <p class="mb-0">نظام استبيان JST</p>
            </div>
            
            <div class="card-body">
                <?php if ($success_message): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo htmlspecialchars($success_message); ?>
                        <br><br>
                        <a href="admin/index.php" class="btn btn-success btn-sm">
                            <i class="fas fa-tachometer-alt me-1"></i>
                            الذهاب إلى لوحة الإدارة
                        </a>
                    </div>
                <?php endif; ?>
                
                <?php if ($error_message): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php echo htmlspecialchars($error_message); ?>
                    </div>
                <?php endif; ?>
                
                <!-- الإحصائيات الحالية -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="stats-card">
                            <h4><?php echo $stats['companies']; ?></h4>
                            <p class="mb-0">شركة</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="stats-card">
                            <h4><?php echo $stats['branches']; ?></h4>
                            <p class="mb-0">فرع</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="stats-card">
                            <h4><?php echo $stats['sales_reps']; ?></h4>
                            <p class="mb-0">مندوب مبيعات</p>
                        </div>
                    </div>
                </div>
                
                <!-- معاينة البيانات التي سيتم إضافتها -->
                <div class="row">
                    <div class="col-md-6">
                        <h5><i class="fas fa-building me-2"></i>الشركات (2)</h5>
                        <div class="data-preview">
                            <small>
                                01 - شركة أنظمة جدة للتجارة<br>
                                02 - شركة حلم المنزل للأثاث
                            </small>
                        </div>
                        
                        <h5><i class="fas fa-store me-2"></i>الفروع (7)</h5>
                        <div class="data-preview">
                            <small>
                                0109 - المتجر انظمة<br>
                                0101 - فرع العليا انظمة<br>
                                0105 - مشاريع انظمة<br>
                                0201 - فرع العليا حلم<br>
                                0209 - المتجر حلم<br>
                                0203 - فرع صحاري مول، بوابه ٦<br>
                                0205 - مشاريع حلم
                            </small>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <h5><i class="fas fa-users me-2"></i>مندوبي المبيعات (19)</h5>
                        <div class="data-preview">
                            <small>
                                ID0003 - هاني سالم<br>
                                ID0012 - خالد سالم<br>
                                ID0030 - عبدالله صلاح<br>
                                ID0141 - مصطفى عبدالله<br>
                                ID0041 - عبد المجيد محمد<br>
                                ID0025 - عيسى سالم<br>
                                ID0105 - ياسر فرحان<br>
                                ID0122 - سعد محمد<br>
                                ID0139 - زيفه ناصر<br>
                                ID0142 - شهد محمد<br>
                                ID0143 - عبد الرحمن صالح<br>
                                ID0145 - شريفه حسين<br>
                                ID0146 - مها عمر<br>
                                ID0147 - دعاء حسان<br>
                                ID0148 - ناديه جمال<br>
                                ID0011 - مرعي عمر<br>
                                ID0022 - روان الدوسري<br>
                                ID0028 - محمد الشاطري<br>
                                ID0014 - عزيز محمود
                            </small>
                        </div>
                    </div>
                </div>
                
                <div class="alert alert-warning mt-4">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تنبيه مهم:</strong>
                    <ul class="mb-0 mt-2">
                        <li>سيتم حذف جميع البيانات الموجودة للشركات والفروع ومندوبي المبيعات</li>
                        <li>سيتم حذف جميع ردود الاستبيانات المرتبطة بهذه البيانات</li>
                        <li>هذه العملية لا يمكن التراجع عنها</li>
                    </ul>
                </div>
                
                <form method="POST" class="text-center">
                    <button type="submit" name="add_data" class="btn btn-primary btn-lg" 
                            onclick="return confirm('هل أنت متأكد من إضافة البيانات الافتراضية؟ سيتم حذف البيانات الموجودة.')">
                        <i class="fas fa-plus-circle me-2"></i>
                        إضافة البيانات الافتراضية
                    </button>
                </form>
                
                <div class="text-center mt-3">
                    <a href="admin/index.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة إلى لوحة الإدارة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
