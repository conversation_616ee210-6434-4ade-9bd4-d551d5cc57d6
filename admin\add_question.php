<?php
/**
 * إضافة سؤال جديد
 * Add New Question
 */

define('INCLUDED_FROM_APP', true);
require_once '../config.php';

// التحقق من تسجيل الدخول بشكل مبسط
session_start();
if (!isset($_SESSION['user_id'])) {
    header("Location: simple_login.php");
    exit();
}

$page_title = 'إضافة سؤال جديد';
$success_message = '';
$error_message = '';

// معالجة إضافة السؤال
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $question_text = trim($_POST['question_text'] ?? '');
    $question_type = $_POST['question_type'] ?? 'rating';
    $is_required = isset($_POST['is_required']) ? 1 : 0;
    $question_order = intval($_POST['question_order'] ?? 1);
    
    if (empty($question_text)) {
        $error_message = 'نص السؤال مطلوب';
    } else {
        try {
            // التحقق من عدم تكرار الترتيب
            $stmt = $pdo->prepare("SELECT id FROM survey_questions WHERE question_order = ?");
            $stmt->execute([$question_order]);
            if ($stmt->fetch()) {
                // تحديث ترتيب الأسئلة الأخرى
                $stmt = $pdo->prepare("UPDATE survey_questions SET question_order = question_order + 1 WHERE question_order >= ?");
                $stmt->execute([$question_order]);
            }
            
            // إضافة السؤال الجديد
            $stmt = $pdo->prepare("
                INSERT INTO survey_questions (question_text, question_type, is_required, question_order, is_active) 
                VALUES (?, ?, ?, ?, 1)
            ");
            $stmt->execute([$question_text, $question_type, $is_required, $question_order]);
            
            $success_message = 'تم إضافة السؤال بنجاح';
            
            // إعادة توجيه لصفحة الإعدادات
            header("Location: survey_settings.php");
            exit();
            
        } catch (Exception $e) {
            $error_message = 'حدث خطأ: ' . $e->getMessage();
        }
    }
}

// جلب أكبر رقم ترتيب
$stmt = $pdo->query("SELECT MAX(question_order) as max_order FROM survey_questions");
$result = $stmt->fetch();
$next_order = ($result['max_order'] ?? 0) + 1;

include 'includes/header.php';
?>

<div class="page-header">
    <h1><i class="fas fa-plus-circle me-3"></i><?php echo $page_title; ?></h1>
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="index.php">لوحة المعلومات</a></li>
            <li class="breadcrumb-item"><a href="survey_settings.php">إعدادات الاستبيان</a></li>
            <li class="breadcrumb-item active">إضافة سؤال جديد</li>
        </ol>
    </nav>
</div>

<?php if ($success_message): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-check-circle me-2"></i>
        <?php echo htmlspecialchars($success_message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($error_message): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <?php echo htmlspecialchars($error_message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<div class="card">
    <div class="card-header">
        <h5><i class="fas fa-question-circle me-2"></i>بيانات السؤال الجديد</h5>
    </div>
    <div class="card-body">
        <form method="POST">
            <div class="row">
                <div class="col-md-8">
                    <div class="mb-3">
                        <label for="question_text" class="form-label">نص السؤال <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="question_text" name="question_text" rows="3" 
                                  maxlength="500" required placeholder="اكتب نص السؤال هنا..."><?php echo htmlspecialchars($_POST['question_text'] ?? ''); ?></textarea>
                        <div class="form-text">الحد الأقصى 500 حرف</div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="question_order" class="form-label">ترتيب السؤال</label>
                        <input type="number" class="form-control" id="question_order" name="question_order" 
                               value="<?php echo $_POST['question_order'] ?? $next_order; ?>" min="1" max="100">
                        <div class="form-text">الترتيب في الاستبيان</div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="question_type" class="form-label">نوع السؤال</label>
                        <select class="form-select" id="question_type" name="question_type">
                            <option value="rating" <?php echo ($_POST['question_type'] ?? 'rating') == 'rating' ? 'selected' : ''; ?>>
                                ⭐ تقييم بالنجوم (1-5)
                            </option>
                            <option value="text" <?php echo ($_POST['question_type'] ?? '') == 'text' ? 'selected' : ''; ?>>
                                📝 نص مفتوح
                            </option>
                        </select>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">خيارات السؤال</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_required" name="is_required" 
                                   <?php echo isset($_POST['is_required']) ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="is_required">
                                <i class="fas fa-exclamation-circle text-danger me-1"></i>
                                سؤال مطلوب (إجباري)
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                <strong>ملاحظات مهمة:</strong>
                <ul class="mb-0 mt-2">
                    <li><strong>تقييم بالنجوم:</strong> يظهر 5 نجوم للتقييم من 1 إلى 5</li>
                    <li><strong>نص مفتوح:</strong> مربع نص يسمح للعميل بكتابة تعليق</li>
                    <li><strong>الترتيب:</strong> يحدد موضع السؤال في الاستبيان</li>
                    <li><strong>السؤال المطلوب:</strong> يجب على العميل الإجابة عليه قبل الإرسال</li>
                </ul>
            </div>
            
            <div class="text-end">
                <a href="survey_settings.php" class="btn btn-secondary me-2">
                    <i class="fas fa-arrow-right me-2"></i>
                    إلغاء
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    إضافة السؤال
                </button>
            </div>
        </form>
    </div>
</div>

<!-- معاينة السؤال -->
<div class="card mt-4">
    <div class="card-header">
        <h5><i class="fas fa-eye me-2"></i>معاينة السؤال</h5>
    </div>
    <div class="card-body">
        <div id="question-preview" class="border rounded p-3 bg-light">
            <div class="mb-3">
                <label class="form-label fw-bold">
                    <span id="preview-order"><?php echo $next_order; ?></span> - 
                    <span id="preview-text">اكتب نص السؤال لرؤية المعاينة...</span>
                    <span id="preview-required" class="text-danger" style="display: none;"> *</span>
                </label>
            </div>
            
            <div id="preview-rating" class="rating-stars">
                <span class="star" data-rating="1">⭐</span>
                <span class="star" data-rating="2">⭐</span>
                <span class="star" data-rating="3">⭐</span>
                <span class="star" data-rating="4">⭐</span>
                <span class="star" data-rating="5">⭐</span>
            </div>
            
            <div id="preview-text-input" style="display: none;">
                <textarea class="form-control" rows="3" placeholder="مساحة للتعليق..."></textarea>
            </div>
        </div>
    </div>
</div>

<script>
// معاينة السؤال في الوقت الفعلي
document.getElementById('question_text').addEventListener('input', function() {
    const text = this.value || 'اكتب نص السؤال لرؤية المعاينة...';
    document.getElementById('preview-text').textContent = text;
});

document.getElementById('question_order').addEventListener('input', function() {
    document.getElementById('preview-order').textContent = this.value || '1';
});

document.getElementById('is_required').addEventListener('change', function() {
    document.getElementById('preview-required').style.display = this.checked ? 'inline' : 'none';
});

document.getElementById('question_type').addEventListener('change', function() {
    const isRating = this.value === 'rating';
    document.getElementById('preview-rating').style.display = isRating ? 'block' : 'none';
    document.getElementById('preview-text-input').style.display = isRating ? 'none' : 'block';
});

// تفعيل النجوم في المعاينة
document.querySelectorAll('.star').forEach(star => {
    star.addEventListener('click', function() {
        const rating = this.dataset.rating;
        document.querySelectorAll('.star').forEach((s, index) => {
            s.style.opacity = index < rating ? '1' : '0.3';
        });
    });
});
</script>

<style>
.rating-stars {
    font-size: 2rem;
    margin: 10px 0;
}

.star {
    cursor: pointer;
    transition: opacity 0.2s;
    margin-right: 5px;
}

.star:hover {
    opacity: 0.7;
}

#question-preview {
    min-height: 120px;
}
</style>

<?php include 'includes/footer.php'; ?>
