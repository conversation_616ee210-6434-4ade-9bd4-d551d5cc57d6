<?php
/**
 * النسخ الاحتياطي والاستعادة
 * Backup and Restore
 */

define('INCLUDED_FROM_APP', true);
require_once '../config.php';
require_once '../includes/security.php';

$page_title = 'النسخ الاحتياطي';

// التحقق من الصلاحيات
if (!hasPermission('super_admin')) {
    showMessage('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error');
    redirect('index.php');
}

$success_message = '';
$error_message = '';

// معالجة إنشاء نسخة احتياطية
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['create_backup'])) {
    try {
        $backup_filename = 'backup_' . date('Y-m-d_H-i-s') . '.sql';
        $backup_path = BACKUPS_DIR . $backup_filename;
        
        // إنشاء النسخة الاحتياطية
        $backup_content = "-- نسخة احتياطية لقاعدة البيانات\n";
        $backup_content .= "-- تاريخ الإنشاء: " . date('Y-m-d H:i:s') . "\n";
        $backup_content .= "-- المستخدم: " . $_SESSION['full_name'] . "\n\n";
        
        $backup_content .= "SET FOREIGN_KEY_CHECKS=0;\n\n";
        
        // جلب جميع الجداول
        $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
        
        foreach ($tables as $table) {
            // هيكل الجدول
            $create_table = $pdo->query("SHOW CREATE TABLE `$table`")->fetch();
            $backup_content .= "-- هيكل الجدول `$table`\n";
            $backup_content .= "DROP TABLE IF EXISTS `$table`;\n";
            $backup_content .= $create_table['Create Table'] . ";\n\n";
            
            // بيانات الجدول
            $rows = $pdo->query("SELECT * FROM `$table`")->fetchAll(PDO::FETCH_ASSOC);
            if (!empty($rows)) {
                $backup_content .= "-- بيانات الجدول `$table`\n";
                
                foreach ($rows as $row) {
                    $values = array_map(function($value) use ($pdo) {
                        return $value === null ? 'NULL' : $pdo->quote($value);
                    }, array_values($row));
                    
                    $backup_content .= "INSERT INTO `$table` VALUES (" . implode(', ', $values) . ");\n";
                }
                $backup_content .= "\n";
            }
        }
        
        $backup_content .= "SET FOREIGN_KEY_CHECKS=1;\n";
        
        // حفظ النسخة الاحتياطية
        if (file_put_contents($backup_path, $backup_content)) {
            logActivity($_SESSION['user_id'], 'create_backup', null, null, null, ['filename' => $backup_filename]);
            $success_message = "تم إنشاء النسخة الاحتياطية بنجاح: $backup_filename";
        } else {
            throw new Exception('فشل في حفظ النسخة الاحتياطية');
        }
        
    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}

// معالجة حذف نسخة احتياطية
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['delete_backup'])) {
    $filename = $_POST['filename'];
    $backup_path = BACKUPS_DIR . $filename;
    
    if (file_exists($backup_path) && unlink($backup_path)) {
        logActivity($_SESSION['user_id'], 'delete_backup', null, null, null, ['filename' => $filename]);
        $success_message = "تم حذف النسخة الاحتياطية بنجاح";
    } else {
        $error_message = "فشل في حذف النسخة الاحتياطية";
    }
}

// معالجة استعادة نسخة احتياطية
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['restore_backup'])) {
    $filename = $_POST['filename'];
    $backup_path = BACKUPS_DIR . $filename;
    
    if (!file_exists($backup_path)) {
        $error_message = "النسخة الاحتياطية غير موجودة";
    } else {
        try {
            $sql_content = file_get_contents($backup_path);
            
            // تقسيم الاستعلامات
            $statements = explode(';', $sql_content);
            
            $pdo->beginTransaction();
            
            foreach ($statements as $statement) {
                $statement = trim($statement);
                if (!empty($statement) && strpos($statement, '--') !== 0) {
                    $pdo->exec($statement);
                }
            }
            
            $pdo->commit();
            
            logActivity($_SESSION['user_id'], 'restore_backup', null, null, null, ['filename' => $filename]);
            $success_message = "تم استعادة النسخة الاحتياطية بنجاح";
            
        } catch (Exception $e) {
            $pdo->rollBack();
            $error_message = "فشل في استعادة النسخة الاحتياطية: " . $e->getMessage();
        }
    }
}

// جلب النسخ الاحتياطية الموجودة
$backups = [];
if (is_dir(BACKUPS_DIR)) {
    $files = scandir(BACKUPS_DIR);
    foreach ($files as $file) {
        if (pathinfo($file, PATHINFO_EXTENSION) == 'sql') {
            $filepath = BACKUPS_DIR . $file;
            $backups[] = [
                'filename' => $file,
                'size' => filesize($filepath),
                'date' => date('Y-m-d H:i:s', filemtime($filepath))
            ];
        }
    }
    
    // ترتيب حسب التاريخ (الأحدث أولاً)
    usort($backups, function($a, $b) {
        return strtotime($b['date']) - strtotime($a['date']);
    });
}

include 'includes/header.php';
?>

<div class="page-header">
    <h1><i class="fas fa-database me-3"></i><?php echo $page_title; ?></h1>
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="index.php">لوحة المعلومات</a></li>
            <li class="breadcrumb-item active">النسخ الاحتياطي</li>
        </ol>
    </nav>
</div>

<?php if ($success_message): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-check-circle me-2"></i>
        <?php echo htmlspecialchars($success_message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($error_message): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <?php echo htmlspecialchars($error_message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<div class="row">
    <!-- إنشاء نسخة احتياطية -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-plus-circle me-2"></i>إنشاء نسخة احتياطية</h5>
            </div>
            <div class="card-body">
                <p class="text-muted">إنشاء نسخة احتياطية كاملة من قاعدة البيانات تشمل جميع الجداول والبيانات.</p>
                
                <form method="POST" data-confirm="هل أنت متأكد من إنشاء نسخة احتياطية؟">
                    <button type="submit" name="create_backup" class="btn btn-primary">
                        <i class="fas fa-download me-2"></i>
                        إنشاء نسخة احتياطية
                    </button>
                </form>
                
                <hr>
                
                <h6>معلومات قاعدة البيانات:</h6>
                <ul class="list-unstyled">
                    <li><strong>اسم قاعدة البيانات:</strong> <?php echo $dbname; ?></li>
                    <li><strong>عدد الجداول:</strong> 
                        <?php 
                        $table_count = $pdo->query("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = '$dbname'")->fetchColumn();
                        echo $table_count;
                        ?>
                    </li>
                    <li><strong>حجم قاعدة البيانات:</strong> 
                        <?php 
                        $size_query = $pdo->query("
                            SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb 
                            FROM information_schema.tables 
                            WHERE table_schema = '$dbname'
                        ");
                        $size = $size_query->fetchColumn();
                        echo $size . ' ميجابايت';
                        ?>
                    </li>
                </ul>
            </div>
        </div>
    </div>
    
    <!-- النسخ الاحتياطية الموجودة -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-archive me-2"></i>النسخ الاحتياطية الموجودة (<?php echo count($backups); ?>)</h5>
            </div>
            <div class="card-body">
                <?php if (empty($backups)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-archive fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد نسخ احتياطية</h5>
                        <p class="text-muted">قم بإنشاء نسخة احتياطية أولاً</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>اسم الملف</th>
                                    <th>الحجم</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($backups as $backup): ?>
                                    <tr>
                                        <td>
                                            <i class="fas fa-file-archive text-primary me-2"></i>
                                            <?php echo htmlspecialchars($backup['filename']); ?>
                                        </td>
                                        <td><?php echo number_format($backup['size'] / 1024, 2); ?> كيلوبايت</td>
                                        <td><?php echo $backup['date']; ?></td>
                                        <td>
                                            <a href="../backups/<?php echo urlencode($backup['filename']); ?>" 
                                               class="btn btn-sm btn-outline-primary" download>
                                                <i class="fas fa-download"></i>
                                                تحميل
                                            </a>
                                            
                                            <button class="btn btn-sm btn-outline-warning" 
                                                    onclick="restoreBackup('<?php echo htmlspecialchars($backup['filename']); ?>')">
                                                <i class="fas fa-undo"></i>
                                                استعادة
                                            </button>
                                            
                                            <button class="btn btn-sm btn-outline-danger" 
                                                    onclick="deleteBackup('<?php echo htmlspecialchars($backup['filename']); ?>')">
                                                <i class="fas fa-trash"></i>
                                                حذف
                                            </button>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- تحذيرات مهمة -->
<div class="card mt-4">
    <div class="card-header bg-warning">
        <h5 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>تحذيرات مهمة</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <h6>قبل إنشاء النسخة الاحتياطية:</h6>
                <ul>
                    <li>تأكد من عدم وجود عمليات كبيرة قيد التنفيذ</li>
                    <li>تأكد من وجود مساحة كافية على القرص الصلب</li>
                    <li>قم بإنشاء نسخ احتياطية بانتظام</li>
                </ul>
            </div>
            <div class="col-md-6">
                <h6>قبل استعادة النسخة الاحتياطية:</h6>
                <ul>
                    <li><strong>تحذير:</strong> ستفقد جميع البيانات الحالية</li>
                    <li>تأكد من أن النسخة الاحتياطية صحيحة</li>
                    <li>قم بإنشاء نسخة احتياطية من البيانات الحالية أولاً</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<?php
$extra_js = "
<script>
function restoreBackup(filename) {
    if (confirm('تحذير: ستفقد جميع البيانات الحالية وسيتم استبدالها بالنسخة الاحتياطية. هل أنت متأكد؟')) {
        if (confirm('هذا الإجراء لا يمكن التراجع عنه. هل أنت متأكد تماماً؟')) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.innerHTML = '<input type=\"hidden\" name=\"filename\" value=\"' + filename + '\">' +
                            '<input type=\"hidden\" name=\"restore_backup\" value=\"1\">';
            document.body.appendChild(form);
            form.submit();
        }
    }
}

function deleteBackup(filename) {
    if (confirm('هل أنت متأكد من حذف النسخة الاحتياطية: ' + filename + '؟')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = '<input type=\"hidden\" name=\"filename\" value=\"' + filename + '\">' +
                        '<input type=\"hidden\" name=\"delete_backup\" value=\"1\">';
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
";

include 'includes/footer.php';
?>
