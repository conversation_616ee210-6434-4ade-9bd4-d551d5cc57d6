<?php
/**
 * الإعدادات العامة
 * General Settings
 */

define('INCLUDED_FROM_APP', true);
require_once '../config.php';
require_once '../includes/security.php';

$page_title = 'الإعدادات العامة';

// التحقق من الصلاحيات
if (!hasPermission('super_admin')) {
    showMessage('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error');
    redirect('index.php');
}

$success_message = '';
$error_message = '';

// معالجة إضافة شركة
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['add_company'])) {
    $code = sanitizeInput($_POST['code'] ?? '');
    $name = sanitizeInput($_POST['name'] ?? '');
    
    if (empty($code) || empty($name)) {
        $error_message = 'كود الشركة والاسم مطلوبان';
    } else {
        try {
            $stmt = $pdo->prepare("SELECT id FROM companies WHERE code = ? AND is_deleted = 0");
            $stmt->execute([$code]);
            if ($stmt->fetch()) {
                throw new Exception('كود الشركة موجود مسبقاً');
            }
            
            $stmt = $pdo->prepare("INSERT INTO companies (code, name) VALUES (?, ?)");
            $stmt->execute([$code, $name]);
            
            logActivity($_SESSION['user_id'], 'create_company', 'companies', $pdo->lastInsertId());
            $success_message = 'تم إضافة الشركة بنجاح';
            
        } catch (Exception $e) {
            $error_message = $e->getMessage();
        }
    }
}

// معالجة إضافة فرع
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['add_branch'])) {
    $code = sanitizeInput($_POST['code'] ?? '');
    $name = sanitizeInput($_POST['name'] ?? '');
    $company_id = intval($_POST['company_id'] ?? 0);
    
    if (empty($code) || empty($name) || !$company_id) {
        $error_message = 'جميع بيانات الفرع مطلوبة';
    } else {
        try {
            $stmt = $pdo->prepare("SELECT id FROM branches WHERE code = ? AND is_deleted = 0");
            $stmt->execute([$code]);
            if ($stmt->fetch()) {
                throw new Exception('كود الفرع موجود مسبقاً');
            }
            
            $stmt = $pdo->prepare("INSERT INTO branches (code, name, company_id) VALUES (?, ?, ?)");
            $stmt->execute([$code, $name, $company_id]);
            
            logActivity($_SESSION['user_id'], 'create_branch', 'branches', $pdo->lastInsertId());
            $success_message = 'تم إضافة الفرع بنجاح';
            
        } catch (Exception $e) {
            $error_message = $e->getMessage();
        }
    }
}

// معالجة إضافة مندوب
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['add_representative'])) {
    $code = sanitizeInput($_POST['code'] ?? '');
    $name = sanitizeInput($_POST['name'] ?? '');
    
    if (empty($code) || empty($name)) {
        $error_message = 'كود المندوب والاسم مطلوبان';
    } else {
        try {
            $stmt = $pdo->prepare("SELECT id FROM sales_representatives WHERE code = ? AND is_deleted = 0");
            $stmt->execute([$code]);
            if ($stmt->fetch()) {
                throw new Exception('كود المندوب موجود مسبقاً');
            }
            
            $stmt = $pdo->prepare("INSERT INTO sales_representatives (code, name) VALUES (?, ?)");
            $stmt->execute([$code, $name]);
            
            logActivity($_SESSION['user_id'], 'create_representative', 'sales_representatives', $pdo->lastInsertId());
            $success_message = 'تم إضافة المندوب بنجاح';
            
        } catch (Exception $e) {
            $error_message = $e->getMessage();
        }
    }
}

// معالجة التحديث والحذف
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action'])) {
    $action = $_POST['action'];
    $table = $_POST['table'];
    $id = intval($_POST['id']);
    
    try {
        if ($action == 'update') {
            $name = sanitizeInput($_POST['name'] ?? '');
            if (empty($name)) {
                throw new Exception('الاسم مطلوب');
            }
            
            $stmt = $pdo->prepare("UPDATE $table SET name = ?, updated_at = NOW() WHERE id = ?");
            $stmt->execute([$name, $id]);
            
            logActivity($_SESSION['user_id'], 'update_' . substr($table, 0, -1), $table, $id);
            $success_message = 'تم التحديث بنجاح';
            
        } elseif ($action == 'delete') {
            $stmt = $pdo->prepare("UPDATE $table SET is_deleted = 1, updated_at = NOW() WHERE id = ?");
            $stmt->execute([$id]);
            
            logActivity($_SESSION['user_id'], 'delete_' . substr($table, 0, -1), $table, $id);
            $success_message = 'تم الحذف بنجاح';
        }
        
    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}

// جلب البيانات
$companies = $pdo->query("SELECT * FROM companies WHERE is_deleted = 0 ORDER BY name")->fetchAll();
$branches = $pdo->query("
    SELECT b.*, c.name as company_name 
    FROM branches b 
    LEFT JOIN companies c ON b.company_id = c.id 
    WHERE b.is_deleted = 0 
    ORDER BY b.name
")->fetchAll();
$representatives = $pdo->query("SELECT * FROM sales_representatives WHERE is_deleted = 0 ORDER BY name")->fetchAll();

include 'includes/header.php';
?>

<div class="page-header">
    <h1><i class="fas fa-tools me-3"></i><?php echo $page_title; ?></h1>
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="index.php">لوحة المعلومات</a></li>
            <li class="breadcrumb-item active">الإعدادات العامة</li>
        </ol>
    </nav>
</div>

<?php if ($success_message): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-check-circle me-2"></i>
        <?php echo htmlspecialchars($success_message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($error_message): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <?php echo htmlspecialchars($error_message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<div class="row">
    <!-- إدارة الشركات -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-building me-2"></i>إدارة الشركات</h5>
            </div>
            <div class="card-body">
                <!-- إضافة شركة جديدة -->
                <form method="POST" class="mb-4">
                    <div class="mb-3">
                        <label for="company_code" class="form-label">كود الشركة</label>
                        <input type="text" class="form-control" id="company_code" name="code" required>
                    </div>
                    <div class="mb-3">
                        <label for="company_name" class="form-label">اسم الشركة</label>
                        <input type="text" class="form-control" id="company_name" name="name" required>
                    </div>
                    <button type="submit" name="add_company" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus me-2"></i>إضافة شركة
                    </button>
                </form>
                
                <hr>
                
                <!-- قائمة الشركات -->
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>الكود</th>
                                <th>الاسم</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($companies as $company): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($company['code']); ?></td>
                                    <td><?php echo htmlspecialchars($company['name']); ?></td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary" onclick="editItem('companies', <?php echo $company['id']; ?>, '<?php echo htmlspecialchars($company['name']); ?>')">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger" onclick="deleteItem('companies', <?php echo $company['id']; ?>)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- إدارة الفروع -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-store me-2"></i>إدارة الفروع</h5>
            </div>
            <div class="card-body">
                <!-- إضافة فرع جديد -->
                <form method="POST" class="mb-4">
                    <div class="mb-3">
                        <label for="branch_code" class="form-label">كود الفرع</label>
                        <input type="text" class="form-control" id="branch_code" name="code" required>
                    </div>
                    <div class="mb-3">
                        <label for="branch_name" class="form-label">اسم الفرع</label>
                        <input type="text" class="form-control" id="branch_name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="branch_company" class="form-label">الشركة</label>
                        <select class="form-select" id="branch_company" name="company_id" required>
                            <option value="">اختر الشركة</option>
                            <?php foreach ($companies as $company): ?>
                                <option value="<?php echo $company['id']; ?>">
                                    <?php echo htmlspecialchars($company['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <button type="submit" name="add_branch" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus me-2"></i>إضافة فرع
                    </button>
                </form>
                
                <hr>
                
                <!-- قائمة الفروع -->
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>الكود</th>
                                <th>الاسم</th>
                                <th>الشركة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($branches as $branch): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($branch['code']); ?></td>
                                    <td><?php echo htmlspecialchars($branch['name']); ?></td>
                                    <td><small><?php echo htmlspecialchars($branch['company_name'] ?? 'غير محدد'); ?></small></td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary" onclick="editItem('branches', <?php echo $branch['id']; ?>, '<?php echo htmlspecialchars($branch['name']); ?>')">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger" onclick="deleteItem('branches', <?php echo $branch['id']; ?>)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- إدارة المندوبين -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-user-tie me-2"></i>إدارة المندوبين</h5>
            </div>
            <div class="card-body">
                <!-- إضافة مندوب جديد -->
                <form method="POST" class="mb-4">
                    <div class="mb-3">
                        <label for="rep_code" class="form-label">كود المندوب</label>
                        <input type="text" class="form-control" id="rep_code" name="code" required>
                    </div>
                    <div class="mb-3">
                        <label for="rep_name" class="form-label">اسم المندوب</label>
                        <input type="text" class="form-control" id="rep_name" name="name" required>
                    </div>
                    <button type="submit" name="add_representative" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus me-2"></i>إضافة مندوب
                    </button>
                </form>
                
                <hr>
                
                <!-- قائمة المندوبين -->
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>الكود</th>
                                <th>الاسم</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($representatives as $rep): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($rep['code']); ?></td>
                                    <td><?php echo htmlspecialchars($rep['name']); ?></td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary" onclick="editItem('sales_representatives', <?php echo $rep['id']; ?>, '<?php echo htmlspecialchars($rep['name']); ?>')">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger" onclick="deleteItem('sales_representatives', <?php echo $rep['id']; ?>)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal تعديل العنصر -->
<div class="modal fade" id="editModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعديل العنصر</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="editForm">
                <div class="modal-body">
                    <input type="hidden" name="action" value="update">
                    <input type="hidden" name="table" id="edit_table">
                    <input type="hidden" name="id" id="edit_id">
                    
                    <div class="mb-3">
                        <label for="edit_name" class="form-label">الاسم</label>
                        <input type="text" class="form-control" id="edit_name" name="name" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
$extra_js = "
<script>
function editItem(table, id, name) {
    document.getElementById('edit_table').value = table;
    document.getElementById('edit_id').value = id;
    document.getElementById('edit_name').value = name;
    
    new bootstrap.Modal(document.getElementById('editModal')).show();
}

function deleteItem(table, id) {
    if (confirm('هل أنت متأكد من الحذف؟')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = '<input type=\"hidden\" name=\"action\" value=\"delete\">' +
                        '<input type=\"hidden\" name=\"table\" value=\"' + table + '\">' +
                        '<input type=\"hidden\" name=\"id\" value=\"' + id + '\">';
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
";

include 'includes/footer.php';
?>
