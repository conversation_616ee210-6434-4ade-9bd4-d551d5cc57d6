<?php
/**
 * لوحة المعلومات الرئيسية
 * Main Dashboard
 */

define('INCLUDED_FROM_APP', true);
require_once '../config.php';
require_once '../includes/security.php';

// التحقق من تسجيل الدخول بشكل مبسط
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}

$page_title = 'لوحة المعلومات';

// جلب الإحصائيات العامة
$stats = [];

// إجمالي الردود
$stmt = $pdo->query("SELECT COUNT(*) as total FROM survey_responses");
$stats['total_responses'] = $stmt->fetch()['total'];

// الردود اليوم
$stmt = $pdo->query("SELECT COUNT(*) as today FROM survey_responses WHERE DATE(submitted_at) = CURDATE()");
$stats['today_responses'] = $stmt->fetch()['today'];

// الردود هذا الأسبوع
$stmt = $pdo->query("SELECT COUNT(*) as week FROM survey_responses WHERE submitted_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)");
$stats['week_responses'] = $stmt->fetch()['week'];

// الردود هذا الشهر
$stmt = $pdo->query("SELECT COUNT(*) as month FROM survey_responses WHERE submitted_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)");
$stats['month_responses'] = $stmt->fetch()['month'];

// متوسط التقييم العام
$stmt = $pdo->query("
    SELECT AVG(rating_value) as avg_rating 
    FROM survey_answers 
    WHERE rating_value IS NOT NULL
");
$result = $stmt->fetch();
$stats['avg_rating'] = $result['avg_rating'] ? round($result['avg_rating'], 1) : 0;

// إحصائيات الشركات
$stmt = $pdo->query("
    SELECT c.name, COUNT(sr.id) as responses_count
    FROM companies c
    LEFT JOIN survey_responses sr ON c.code = sr.company_code
    WHERE c.is_active = 1 AND c.is_deleted = 0
    GROUP BY c.id, c.name
    ORDER BY responses_count DESC
");
$company_stats = $stmt->fetchAll();

// إحصائيات التقييمات (للرسم البياني)
$stmt = $pdo->query("
    SELECT rating_value, COUNT(*) as count
    FROM survey_answers 
    WHERE rating_value IS NOT NULL
    GROUP BY rating_value
    ORDER BY rating_value
");
$rating_stats = $stmt->fetchAll();

// الردود الأخيرة
$stmt = $pdo->query("
    SELECT sr.*, c.name as company_name, b.name as branch_name, rep.name as rep_name
    FROM survey_responses sr
    LEFT JOIN companies c ON sr.company_code = c.code
    LEFT JOIN branches b ON sr.branch_code = b.code
    LEFT JOIN sales_representatives rep ON sr.sales_rep_code = rep.code
    ORDER BY sr.submitted_at DESC
    LIMIT 10
");
$recent_responses = $stmt->fetchAll();

// بيانات الرسم البياني للأيام السبعة الماضية
$stmt = $pdo->query("
    SELECT DATE(submitted_at) as date, COUNT(*) as count
    FROM survey_responses 
    WHERE submitted_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
    GROUP BY DATE(submitted_at)
    ORDER BY date
");
$daily_stats = $stmt->fetchAll();

include 'includes/header.php';
?>

<div class="page-header">
    <h1><i class="fas fa-tachometer-alt me-3"></i><?php echo $page_title; ?></h1>
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item active">لوحة المعلومات</li>
        </ol>
    </nav>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-right-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            إجمالي الردود
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?php echo number_format($stats['total_responses']); ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-comments fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-right-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            ردود اليوم
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?php echo number_format($stats['today_responses']); ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-calendar-day fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-right-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            ردود هذا الأسبوع
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?php echo number_format($stats['week_responses']); ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-calendar-week fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-right-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            متوسط التقييم
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?php echo $stats['avg_rating']; ?>/5
                            <div class="rating-stars-small">
                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                    <i class="fas fa-star <?php echo $i <= $stats['avg_rating'] ? 'text-warning' : 'text-muted'; ?>"></i>
                                <?php endfor; ?>
                            </div>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-star fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- الرسم البياني للردود اليومية -->
    <div class="col-xl-8 col-lg-7">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">الردود خلال الأيام السبعة الماضية</h6>
            </div>
            <div class="card-body">
                <div class="chart-area">
                    <canvas id="dailyChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- توزيع التقييمات -->
    <div class="col-xl-4 col-lg-5">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">توزيع التقييمات</h6>
            </div>
            <div class="card-body">
                <div class="chart-pie pt-4 pb-2">
                    <canvas id="ratingChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- إحصائيات الشركات -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">إحصائيات الشركات</h6>
            </div>
            <div class="card-body">
                <?php foreach ($company_stats as $company): ?>
                <div class="d-flex align-items-center mb-3">
                    <div class="flex-grow-1">
                        <h6 class="mb-1"><?php echo htmlspecialchars($company['name']); ?></h6>
                        <div class="progress" style="height: 10px;">
                            <div class="progress-bar bg-primary" role="progressbar" 
                                 style="width: <?php echo $stats['total_responses'] > 0 ? ($company['responses_count'] / $stats['total_responses']) * 100 : 0; ?>%">
                            </div>
                        </div>
                    </div>
                    <div class="ms-3">
                        <span class="badge bg-primary"><?php echo number_format($company['responses_count']); ?></span>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>

    <!-- الردود الأخيرة -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">الردود الأخيرة</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>الفاتورة</th>
                                <th>الشركة</th>
                                <th>التاريخ</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($recent_responses as $response): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($response['invoice_id']); ?></td>
                                <td>
                                    <small class="text-muted">
                                        <?php echo htmlspecialchars($response['company_name'] ?? 'غير محدد'); ?>
                                    </small>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        <?php echo date('Y-m-d H:i', strtotime($response['submitted_at'])); ?>
                                    </small>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <div class="text-center">
                    <a href="reports.php" class="btn btn-primary btn-sm">
                        <i class="fas fa-eye me-2"></i>
                        عرض جميع التقارير
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$extra_js = "
<script>
// الرسم البياني للردود اليومية
const dailyCtx = document.getElementById('dailyChart').getContext('2d');
const dailyChart = new Chart(dailyCtx, {
    type: 'line',
    data: {
        labels: [" . implode(',', array_map(function($item) { return "'" . date('m/d', strtotime($item['date'])) . "'"; }, $daily_stats)) . "],
        datasets: [{
            label: 'عدد الردود',
            data: [" . implode(',', array_column($daily_stats, 'count')) . "],
            borderColor: 'rgb(102, 126, 234)',
            backgroundColor: 'rgba(102, 126, 234, 0.1)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// الرسم البياني لتوزيع التقييمات
const ratingCtx = document.getElementById('ratingChart').getContext('2d');
const ratingChart = new Chart(ratingCtx, {
    type: 'doughnut',
    data: {
        labels: [" . implode(',', array_map(function($item) { return "'" . $item['rating_value'] . " نجوم'"; }, $rating_stats)) . "],
        datasets: [{
            data: [" . implode(',', array_column($rating_stats, 'count')) . "],
            backgroundColor: [
                '#dc3545',
                '#fd7e14',
                '#ffc107',
                '#28a745',
                '#20c997'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
</script>
";

include 'includes/footer.php';
?>
