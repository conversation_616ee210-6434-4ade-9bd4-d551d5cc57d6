<?php
/**
 * سجل العمليات
 * Activity Logs
 */

define('INCLUDED_FROM_APP', true);
require_once '../config.php';
require_once '../includes/security.php';

$page_title = 'سجل العمليات';

// التحقق من الصلاحيات
if (!hasPermission('super_admin')) {
    showMessage('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error');
    redirect('index.php');
}

// معالجة الفلاتر
$filters = [
    'date_from' => $_GET['date_from'] ?? '',
    'date_to' => $_GET['date_to'] ?? '',
    'user_id' => $_GET['user_id'] ?? '',
    'action' => $_GET['action'] ?? '',
    'table_name' => $_GET['table_name'] ?? ''
];

// بناء استعلام السجلات
$where_conditions = [];
$params = [];

if (!empty($filters['date_from'])) {
    $where_conditions[] = "al.created_at >= ?";
    $params[] = $filters['date_from'] . ' 00:00:00';
}

if (!empty($filters['date_to'])) {
    $where_conditions[] = "al.created_at <= ?";
    $params[] = $filters['date_to'] . ' 23:59:59';
}

if (!empty($filters['user_id'])) {
    $where_conditions[] = "al.user_id = ?";
    $params[] = $filters['user_id'];
}

if (!empty($filters['action'])) {
    $where_conditions[] = "al.action LIKE ?";
    $params[] = '%' . $filters['action'] . '%';
}

if (!empty($filters['table_name'])) {
    $where_conditions[] = "al.table_name = ?";
    $params[] = $filters['table_name'];
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// جلب السجلات
$sql = "
    SELECT 
        al.*,
        u.full_name as user_name,
        u.username
    FROM activity_logs al
    LEFT JOIN users u ON al.user_id = u.id
    $where_clause
    ORDER BY al.created_at DESC
    LIMIT 1000
";

$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$logs = $stmt->fetchAll();

// جلب المستخدمين للفلتر
$users = $pdo->query("SELECT id, username, full_name FROM users WHERE is_deleted = 0 ORDER BY full_name")->fetchAll();

// جلب الأعمال المختلفة
$actions = $pdo->query("SELECT DISTINCT action FROM activity_logs ORDER BY action")->fetchAll();

// جلب الجداول المختلفة
$tables = $pdo->query("SELECT DISTINCT table_name FROM activity_logs WHERE table_name IS NOT NULL ORDER BY table_name")->fetchAll();

include 'includes/header.php';
?>

<div class="page-header">
    <h1><i class="fas fa-list-alt me-3"></i><?php echo $page_title; ?></h1>
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="index.php">لوحة المعلومات</a></li>
            <li class="breadcrumb-item active">سجل العمليات</li>
        </ol>
    </nav>
</div>

<!-- فلاتر البحث -->
<div class="card mb-4">
    <div class="card-header">
        <h5><i class="fas fa-filter me-2"></i>فلاتر البحث</h5>
    </div>
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-2">
                <label for="date_from" class="form-label">من تاريخ</label>
                <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo htmlspecialchars($filters['date_from']); ?>">
            </div>
            
            <div class="col-md-2">
                <label for="date_to" class="form-label">إلى تاريخ</label>
                <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo htmlspecialchars($filters['date_to']); ?>">
            </div>
            
            <div class="col-md-2">
                <label for="user_id" class="form-label">المستخدم</label>
                <select class="form-select" id="user_id" name="user_id">
                    <option value="">جميع المستخدمين</option>
                    <?php foreach ($users as $user): ?>
                        <option value="<?php echo $user['id']; ?>" 
                                <?php echo $filters['user_id'] == $user['id'] ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($user['full_name']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="col-md-2">
                <label for="action" class="form-label">العملية</label>
                <select class="form-select" id="action" name="action">
                    <option value="">جميع العمليات</option>
                    <?php foreach ($actions as $action): ?>
                        <option value="<?php echo htmlspecialchars($action['action']); ?>" 
                                <?php echo $filters['action'] == $action['action'] ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($action['action']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="col-md-2">
                <label for="table_name" class="form-label">الجدول</label>
                <select class="form-select" id="table_name" name="table_name">
                    <option value="">جميع الجداول</option>
                    <?php foreach ($tables as $table): ?>
                        <option value="<?php echo htmlspecialchars($table['table_name']); ?>" 
                                <?php echo $filters['table_name'] == $table['table_name'] ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($table['table_name']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="col-md-2 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-search me-2"></i>
                    بحث
                </button>
                <a href="logs.php" class="btn btn-secondary">
                    <i class="fas fa-times me-2"></i>
                    إلغاء
                </a>
            </div>
        </form>
    </div>
</div>

<!-- نتائج السجلات -->
<div class="card">
    <div class="card-header">
        <h5><i class="fas fa-table me-2"></i>سجل العمليات (<?php echo count($logs); ?> عملية)</h5>
    </div>
    <div class="card-body">
        <?php if (empty($logs)): ?>
            <div class="text-center py-5">
                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد عمليات</h5>
                <p class="text-muted">جرب تغيير معايير البحث</p>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-striped table-sm">
                    <thead>
                        <tr>
                            <th>التاريخ والوقت</th>
                            <th>المستخدم</th>
                            <th>العملية</th>
                            <th>الجدول</th>
                            <th>معرف السجل</th>
                            <th>عنوان IP</th>
                            <th>التفاصيل</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($logs as $log): ?>
                            <tr>
                                <td>
                                    <small><?php echo date('Y-m-d H:i:s', strtotime($log['created_at'])); ?></small>
                                </td>
                                <td>
                                    <?php if ($log['user_name']): ?>
                                        <strong><?php echo htmlspecialchars($log['user_name']); ?></strong>
                                        <br><small class="text-muted">@<?php echo htmlspecialchars($log['username']); ?></small>
                                    <?php else: ?>
                                        <span class="text-muted">مستخدم محذوف</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge bg-<?php 
                                        echo strpos($log['action'], 'create') !== false ? 'success' : 
                                            (strpos($log['action'], 'update') !== false ? 'warning' : 
                                            (strpos($log['action'], 'delete') !== false ? 'danger' : 'info')); 
                                    ?>">
                                        <?php echo htmlspecialchars($log['action']); ?>
                                    </span>
                                </td>
                                <td><?php echo htmlspecialchars($log['table_name'] ?? 'غير محدد'); ?></td>
                                <td><?php echo htmlspecialchars($log['record_id'] ?? 'غير محدد'); ?></td>
                                <td><small><?php echo htmlspecialchars($log['ip_address'] ?? 'غير محدد'); ?></small></td>
                                <td>
                                    <?php if ($log['old_values'] || $log['new_values']): ?>
                                        <button class="btn btn-sm btn-outline-info" onclick="showLogDetails(<?php echo $log['id']; ?>)">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Modal تفاصيل السجل -->
<div class="modal fade" id="logDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل العملية</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="logDetailsContent">
                <!-- سيتم تحميل المحتوى هنا -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<?php
$logs_json = json_encode($logs);
$extra_js = "
<script>
const logs = $logs_json;

function showLogDetails(logId) {
    const log = logs.find(l => l.id == logId);
    if (!log) return;
    
    let content = '<div class=\"row\">';
    
    // معلومات العملية
    content += '<div class=\"col-md-6\">';
    content += '<h6>معلومات العملية</h6>';
    content += '<table class=\"table table-sm table-borderless\">';
    content += '<tr><td><strong>التاريخ:</strong></td><td>' + log.created_at + '</td></tr>';
    content += '<tr><td><strong>المستخدم:</strong></td><td>' + (log.user_name || 'غير محدد') + '</td></tr>';
    content += '<tr><td><strong>العملية:</strong></td><td>' + log.action + '</td></tr>';
    content += '<tr><td><strong>الجدول:</strong></td><td>' + (log.table_name || 'غير محدد') + '</td></tr>';
    content += '<tr><td><strong>معرف السجل:</strong></td><td>' + (log.record_id || 'غير محدد') + '</td></tr>';
    content += '<tr><td><strong>عنوان IP:</strong></td><td>' + (log.ip_address || 'غير محدد') + '</td></tr>';
    content += '</table>';
    content += '</div>';
    
    // القيم القديمة والجديدة
    content += '<div class=\"col-md-6\">';
    if (log.old_values) {
        content += '<h6>القيم القديمة</h6>';
        content += '<pre class=\"bg-light p-2 rounded\">' + JSON.stringify(JSON.parse(log.old_values), null, 2) + '</pre>';
    }
    if (log.new_values) {
        content += '<h6>القيم الجديدة</h6>';
        content += '<pre class=\"bg-light p-2 rounded\">' + JSON.stringify(JSON.parse(log.new_values), null, 2) + '</pre>';
    }
    content += '</div>';
    
    content += '</div>';
    
    document.getElementById('logDetailsContent').innerHTML = content;
    new bootstrap.Modal(document.getElementById('logDetailsModal')).show();
}
</script>
";

include 'includes/footer.php';
?>
