<?php
/**
 * الملف الشخصي للمستخدم
 * User Profile
 */

define('INCLUDED_FROM_APP', true);
require_once '../config.php';
require_once '../includes/security.php';

$page_title = 'الملف الشخصي';

// جلب بيانات المستخدم الحالي
$stmt = $pdo->prepare("SELECT * FROM users WHERE id = ? AND is_deleted = 0");
$stmt->execute([$_SESSION['user_id']]);
$user = $stmt->fetch();

if (!$user) {
    redirect('logout.php');
}

$success_message = '';
$error_message = '';

// معالجة تحديث البيانات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['update_profile'])) {
        $full_name = sanitizeInput($_POST['full_name'] ?? '');
        $email = sanitizeInput($_POST['email'] ?? '');
        $phone = sanitizeInput($_POST['phone'] ?? '');
        
        if (empty($full_name)) {
            $error_message = 'الاسم الكامل مطلوب';
        } elseif (!empty($email) && !validateEmail($email)) {
            $error_message = 'البريد الإلكتروني غير صحيح';
        } else {
            try {
                // التحقق من عدم تكرار البريد الإلكتروني
                if (!empty($email)) {
                    $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ? AND id != ? AND is_deleted = 0");
                    $stmt->execute([$email, $_SESSION['user_id']]);
                    if ($stmt->fetch()) {
                        throw new Exception('البريد الإلكتروني مستخدم من قبل مستخدم آخر');
                    }
                }
                
                $stmt = $pdo->prepare("
                    UPDATE users 
                    SET full_name = ?, email = ?, phone = ?, updated_at = NOW() 
                    WHERE id = ?
                ");
                $stmt->execute([$full_name, $email ?: null, $phone ?: null, $_SESSION['user_id']]);
                
                // تحديث الجلسة
                $_SESSION['full_name'] = $full_name;
                $_SESSION['email'] = $email;
                
                // تسجيل العملية
                logActivity($_SESSION['user_id'], 'update_profile', 'users', $_SESSION['user_id']);
                
                $success_message = 'تم تحديث البيانات بنجاح';
                
                // إعادة جلب البيانات المحدثة
                $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
                $stmt->execute([$_SESSION['user_id']]);
                $user = $stmt->fetch();
                
            } catch (Exception $e) {
                $error_message = $e->getMessage();
            }
        }
    }
    
    // معالجة تغيير كلمة المرور
    elseif (isset($_POST['change_password'])) {
        $current_password = $_POST['current_password'] ?? '';
        $new_password = $_POST['new_password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';
        
        if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
            $error_message = 'جميع حقول كلمة المرور مطلوبة';
        } elseif (!verifyPassword($current_password, $user['password'])) {
            $error_message = 'كلمة المرور الحالية غير صحيحة';
        } elseif ($new_password !== $confirm_password) {
            $error_message = 'كلمة المرور الجديدة وتأكيدها غير متطابقتين';
        } elseif (!validatePassword($new_password)) {
            $error_message = 'كلمة المرور يجب أن تحتوي على 8 أحرف على الأقل وتشمل حروف كبيرة وصغيرة وأرقام';
        } else {
            try {
                $hashed_password = hashPassword($new_password);
                
                $stmt = $pdo->prepare("UPDATE users SET password = ?, updated_at = NOW() WHERE id = ?");
                $stmt->execute([$hashed_password, $_SESSION['user_id']]);
                
                // تسجيل العملية
                logActivity($_SESSION['user_id'], 'change_password', 'users', $_SESSION['user_id']);
                
                $success_message = 'تم تغيير كلمة المرور بنجاح';
                
            } catch (Exception $e) {
                $error_message = $e->getMessage();
            }
        }
    }
}

include 'includes/header.php';
?>

<div class="page-header">
    <h1><i class="fas fa-user-circle me-3"></i><?php echo $page_title; ?></h1>
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="index.php">لوحة المعلومات</a></li>
            <li class="breadcrumb-item active">الملف الشخصي</li>
        </ol>
    </nav>
</div>

<?php if ($success_message): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-check-circle me-2"></i>
        <?php echo htmlspecialchars($success_message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($error_message): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <?php echo htmlspecialchars($error_message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<div class="row">
    <!-- معلومات المستخدم -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-user me-2"></i>معلومات المستخدم</h5>
            </div>
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-user-circle fa-5x text-muted"></i>
                </div>
                <h5><?php echo htmlspecialchars($user['full_name']); ?></h5>
                <p class="text-muted">@<?php echo htmlspecialchars($user['username']); ?></p>
                
                <div class="row text-center">
                    <div class="col">
                        <span class="badge bg-<?php echo $user['role'] == 'super_admin' ? 'danger' : ($user['role'] == 'manager' ? 'warning' : 'info'); ?>">
                            <?php 
                            $roles = [
                                'super_admin' => 'مدير عام',
                                'manager' => 'مدير',
                                'user' => 'مستخدم'
                            ];
                            echo $roles[$user['role']] ?? $user['role'];
                            ?>
                        </span>
                    </div>
                </div>
                
                <hr>
                
                <div class="row text-center">
                    <div class="col-6">
                        <small class="text-muted">تاريخ الإنشاء</small>
                        <div><?php echo date('Y-m-d', strtotime($user['created_at'])); ?></div>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">آخر دخول</small>
                        <div><?php echo $user['last_login'] ? date('Y-m-d H:i', strtotime($user['last_login'])) : 'لم يسجل دخول'; ?></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- تحديث البيانات -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-edit me-2"></i>تحديث البيانات الشخصية</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="username" class="form-label">اسم المستخدم</label>
                                <input type="text" class="form-control" id="username" value="<?php echo htmlspecialchars($user['username']); ?>" disabled>
                                <small class="text-muted">لا يمكن تغيير اسم المستخدم</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="full_name" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="full_name" name="full_name" value="<?php echo htmlspecialchars($user['full_name']); ?>" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="email" name="email" value="<?php echo htmlspecialchars($user['email'] ?? ''); ?>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="phone" class="form-label">رقم الجوال</label>
                                <input type="tel" class="form-control" id="phone" name="phone" value="<?php echo htmlspecialchars($user['phone'] ?? ''); ?>">
                            </div>
                        </div>
                    </div>
                    
                    <div class="text-end">
                        <button type="submit" name="update_profile" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            حفظ التغييرات
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- تغيير كلمة المرور -->
        <div class="card mt-4">
            <div class="card-header">
                <h5><i class="fas fa-lock me-2"></i>تغيير كلمة المرور</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label for="current_password" class="form-label">كلمة المرور الحالية <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="current_password" name="current_password" required>
                                    <button class="btn btn-outline-secondary password-toggle" type="button">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="new_password" class="form-label">كلمة المرور الجديدة <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="new_password" name="new_password" required>
                                    <button class="btn btn-outline-secondary password-toggle" type="button">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <small class="text-muted">8 أحرف على الأقل، تشمل حروف كبيرة وصغيرة وأرقام</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="confirm_password" class="form-label">تأكيد كلمة المرور <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                    <button class="btn btn-outline-secondary password-toggle" type="button">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="text-end">
                        <button type="submit" name="change_password" class="btn btn-warning">
                            <i class="fas fa-key me-2"></i>
                            تغيير كلمة المرور
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
