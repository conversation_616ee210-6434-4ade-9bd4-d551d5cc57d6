<?php
/**
 * صفحة التقارير
 * Reports Page
 */

define('INCLUDED_FROM_APP', true);
require_once '../config.php';
require_once '../includes/security.php';

$page_title = 'التقارير';

// معالجة الفلاتر
$filters = [
    'date_from' => $_GET['date_from'] ?? '',
    'date_to' => $_GET['date_to'] ?? '',
    'company_code' => $_GET['company_code'] ?? '',
    'branch_code' => $_GET['branch_code'] ?? '',
    'sales_rep_code' => $_GET['sales_rep_code'] ?? '',
    'rating_min' => $_GET['rating_min'] ?? '',
    'rating_max' => $_GET['rating_max'] ?? ''
];

// بناء استعلام التقارير
$where_conditions = [];
$params = [];

if (!empty($filters['date_from'])) {
    $where_conditions[] = "sr.submitted_at >= ?";
    $params[] = $filters['date_from'] . ' 00:00:00';
}

if (!empty($filters['date_to'])) {
    $where_conditions[] = "sr.submitted_at <= ?";
    $params[] = $filters['date_to'] . ' 23:59:59';
}

if (!empty($filters['company_code'])) {
    $where_conditions[] = "sr.company_code = ?";
    $params[] = $filters['company_code'];
}

if (!empty($filters['branch_code'])) {
    $where_conditions[] = "sr.branch_code = ?";
    $params[] = $filters['branch_code'];
}

if (!empty($filters['sales_rep_code'])) {
    $where_conditions[] = "sr.sales_rep_code = ?";
    $params[] = $filters['sales_rep_code'];
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// جلب البيانات للفلاتر
$companies = $pdo->query("SELECT code, name FROM companies WHERE is_active = 1 AND is_deleted = 0 ORDER BY name")->fetchAll();
$branches = $pdo->query("SELECT code, name FROM branches WHERE is_active = 1 AND is_deleted = 0 ORDER BY name")->fetchAll();
$representatives = $pdo->query("SELECT code, name FROM sales_representatives WHERE is_active = 1 AND is_deleted = 0 ORDER BY name")->fetchAll();

// جلب التقارير
$sql = "
    SELECT 
        sr.*,
        c.name as company_name,
        b.name as branch_name,
        rep.name as rep_name,
        AVG(sa.rating_value) as avg_rating,
        COUNT(sa.id) as answers_count
    FROM survey_responses sr
    LEFT JOIN companies c ON sr.company_code = c.code
    LEFT JOIN branches b ON sr.branch_code = b.code
    LEFT JOIN sales_representatives rep ON sr.sales_rep_code = rep.code
    LEFT JOIN survey_answers sa ON sr.id = sa.response_id
    $where_clause
    GROUP BY sr.id
    ORDER BY sr.submitted_at DESC
";

// تطبيق فلتر التقييم بعد GROUP BY
if (!empty($filters['rating_min']) || !empty($filters['rating_max'])) {
    $having_conditions = [];
    if (!empty($filters['rating_min'])) {
        $having_conditions[] = "AVG(sa.rating_value) >= " . floatval($filters['rating_min']);
    }
    if (!empty($filters['rating_max'])) {
        $having_conditions[] = "AVG(sa.rating_value) <= " . floatval($filters['rating_max']);
    }
    $sql .= " HAVING " . implode(' AND ', $having_conditions);
}

$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$reports = $stmt->fetchAll();

// معالجة التصدير إلى Excel
if (isset($_GET['export']) && $_GET['export'] == 'excel') {
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename=survey_reports_' . date('Y-m-d') . '.csv');
    
    // إضافة BOM للدعم العربي
    echo "\xEF\xBB\xBF";
    
    // رؤوس الأعمدة
    $headers = [
        'رقم الفاتورة',
        'الشركة',
        'الفرع',
        'المندوب',
        'رقم العميل',
        'متوسط التقييم',
        'عدد الإجابات',
        'تاريخ الإرسال',
        'عنوان IP'
    ];
    
    echo implode(',', array_map(function($header) {
        return '"' . str_replace('"', '""', $header) . '"';
    }, $headers)) . "\n";
    
    // البيانات
    foreach ($reports as $report) {
        $row = [
            $report['invoice_id'],
            $report['company_name'] ?? 'غير محدد',
            $report['branch_name'] ?? 'غير محدد',
            $report['rep_name'] ?? 'غير محدد',
            $report['customer_phone'],
            $report['avg_rating'] ? round($report['avg_rating'], 2) : 'لا يوجد',
            $report['answers_count'],
            date('Y-m-d H:i:s', strtotime($report['submitted_at'])),
            $report['ip_address'] ?? 'غير محدد'
        ];
        
        echo implode(',', array_map(function($field) {
            return '"' . str_replace('"', '""', $field) . '"';
        }, $row)) . "\n";
    }
    
    exit;
}

include 'includes/header.php';
?>

<div class="page-header">
    <h1><i class="fas fa-chart-bar me-3"></i><?php echo $page_title; ?></h1>
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="index.php">لوحة المعلومات</a></li>
            <li class="breadcrumb-item active">التقارير</li>
        </ol>
    </nav>
</div>

<!-- فلاتر البحث -->
<div class="card mb-4">
    <div class="card-header">
        <h5><i class="fas fa-filter me-2"></i>فلاتر البحث</h5>
    </div>
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label for="date_from" class="form-label">من تاريخ</label>
                <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo htmlspecialchars($filters['date_from']); ?>">
            </div>
            
            <div class="col-md-3">
                <label for="date_to" class="form-label">إلى تاريخ</label>
                <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo htmlspecialchars($filters['date_to']); ?>">
            </div>
            
            <div class="col-md-3">
                <label for="company_code" class="form-label">الشركة</label>
                <select class="form-select" id="company_code" name="company_code">
                    <option value="">جميع الشركات</option>
                    <?php foreach ($companies as $company): ?>
                        <option value="<?php echo htmlspecialchars($company['code']); ?>" 
                                <?php echo $filters['company_code'] == $company['code'] ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($company['name']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="col-md-3">
                <label for="branch_code" class="form-label">الفرع</label>
                <select class="form-select" id="branch_code" name="branch_code">
                    <option value="">جميع الفروع</option>
                    <?php foreach ($branches as $branch): ?>
                        <option value="<?php echo htmlspecialchars($branch['code']); ?>" 
                                <?php echo $filters['branch_code'] == $branch['code'] ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($branch['name']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="col-md-3">
                <label for="sales_rep_code" class="form-label">المندوب</label>
                <select class="form-select" id="sales_rep_code" name="sales_rep_code">
                    <option value="">جميع المندوبين</option>
                    <?php foreach ($representatives as $rep): ?>
                        <option value="<?php echo htmlspecialchars($rep['code']); ?>" 
                                <?php echo $filters['sales_rep_code'] == $rep['code'] ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($rep['name']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="col-md-3">
                <label for="rating_min" class="form-label">أقل تقييم</label>
                <select class="form-select" id="rating_min" name="rating_min">
                    <option value="">أي تقييم</option>
                    <?php for ($i = 1; $i <= 5; $i++): ?>
                        <option value="<?php echo $i; ?>" <?php echo $filters['rating_min'] == $i ? 'selected' : ''; ?>>
                            <?php echo $i; ?> نجوم
                        </option>
                    <?php endfor; ?>
                </select>
            </div>
            
            <div class="col-md-3">
                <label for="rating_max" class="form-label">أعلى تقييم</label>
                <select class="form-select" id="rating_max" name="rating_max">
                    <option value="">أي تقييم</option>
                    <?php for ($i = 1; $i <= 5; $i++): ?>
                        <option value="<?php echo $i; ?>" <?php echo $filters['rating_max'] == $i ? 'selected' : ''; ?>>
                            <?php echo $i; ?> نجوم
                        </option>
                    <?php endfor; ?>
                </select>
            </div>
            
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-search me-2"></i>
                    بحث
                </button>
                <a href="reports.php" class="btn btn-secondary">
                    <i class="fas fa-times me-2"></i>
                    إلغاء
                </a>
            </div>
        </form>
    </div>
</div>

<!-- نتائج التقارير -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5><i class="fas fa-table me-2"></i>نتائج التقارير (<?php echo count($reports); ?> نتيجة)</h5>
        <div>
            <?php if (!empty($reports)): ?>
                <a href="?<?php echo http_build_query(array_merge($_GET, ['export' => 'excel'])); ?>" class="btn btn-success btn-sm">
                    <i class="fas fa-file-excel me-2"></i>
                    تصدير Excel
                </a>
            <?php endif; ?>
        </div>
    </div>
    <div class="card-body">
        <?php if (empty($reports)): ?>
            <div class="text-center py-5">
                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد نتائج</h5>
                <p class="text-muted">جرب تغيير معايير البحث</p>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-striped" id="reportsTable">
                    <thead>
                        <tr>
                            <th>رقم الفاتورة</th>
                            <th>الشركة</th>
                            <th>الفرع</th>
                            <th>المندوب</th>
                            <th>رقم العميل</th>
                            <th>متوسط التقييم</th>
                            <th>تاريخ الإرسال</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($reports as $report): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($report['invoice_id']); ?></td>
                                <td><?php echo htmlspecialchars($report['company_name'] ?? 'غير محدد'); ?></td>
                                <td><?php echo htmlspecialchars($report['branch_name'] ?? 'غير محدد'); ?></td>
                                <td><?php echo htmlspecialchars($report['rep_name'] ?? 'غير محدد'); ?></td>
                                <td><?php echo htmlspecialchars($report['customer_phone']); ?></td>
                                <td>
                                    <?php if ($report['avg_rating']): ?>
                                        <div class="d-flex align-items-center">
                                            <span class="me-2"><?php echo round($report['avg_rating'], 1); ?></span>
                                            <div class="rating-stars-small">
                                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                                    <i class="fas fa-star <?php echo $i <= $report['avg_rating'] ? 'text-warning' : 'text-muted'; ?>"></i>
                                                <?php endfor; ?>
                                            </div>
                                        </div>
                                    <?php else: ?>
                                        <span class="text-muted">لا يوجد تقييم</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo date('Y-m-d H:i', strtotime($report['submitted_at'])); ?></td>
                                <td>
                                    <a href="view_response.php?id=<?php echo $report['id']; ?>" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                        عرض
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php
$extra_js = "
<script>
// البحث في الجدول
searchTable('searchInput', 'reportsTable');

// تحديث الفروع عند تغيير الشركة
document.getElementById('company_code').addEventListener('change', function() {
    const companyCode = this.value;
    const branchSelect = document.getElementById('branch_code');
    
    // إعادة تعيين الفروع
    branchSelect.innerHTML = '<option value=\"\">جميع الفروع</option>';
    
    if (companyCode) {
        // يمكن إضافة AJAX هنا لجلب الفروع المرتبطة بالشركة
        // أو تحديث الصفحة مع الفلتر الجديد
    }
});
</script>
";

include 'includes/footer.php';
?>
