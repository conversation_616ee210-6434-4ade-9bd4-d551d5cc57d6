<?php
/**
 * معاينة الاستبيان
 * Survey Preview
 */

define('INCLUDED_FROM_APP', true);
require_once '../config.php';

// التحقق من تسجيل الدخول
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
if (!isset($_SESSION['user_id'])) {
    header("Location: simple_login.php");
    exit();
}

$survey_id = intval($_GET['survey_id'] ?? 1);

// جلب بيانات الاستبيان
$stmt = $pdo->prepare("SELECT * FROM surveys WHERE id = ?");
$stmt->execute([$survey_id]);
$survey = $stmt->fetch();

if (!$survey) {
    header("Location: surveys.php");
    exit();
}

// جلب أسئلة الاستبيان
$stmt = $pdo->prepare("
    SELECT * FROM survey_questions 
    WHERE survey_id = ? AND is_active = 1 
    ORDER BY question_order ASC
");
$stmt->execute([$survey_id]);
$questions = $stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($survey['title'] ?? $survey['name']); ?> - معاينة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: <?php echo $survey['theme_color']; ?>;
            --primary-light: <?php echo $survey['theme_color']; ?>20;
            --primary-dark: <?php echo $survey['theme_color']; ?>dd;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        
        .survey-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .survey-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }
        
        .survey-body {
            padding: 40px 30px;
        }
        
        .question-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }
        
        .question-card:hover {
            border-color: var(--primary-color);
            box-shadow: 0 5px 15px var(--primary-light);
        }
        
        .question-number {
            background: var(--primary-color);
            color: white;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-bottom: 15px;
        }
        
        .question-text {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 20px;
            color: #2c3e50;
        }
        
        .rating-stars {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin: 20px 0;
        }
        
        .rating-star {
            font-size: 2.5rem;
            color: #ddd;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .rating-star:hover,
        .rating-star.active {
            color: #ffc107;
            transform: scale(1.1);
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 15px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem var(--primary-light);
        }
        
        .required-badge {
            background: #dc3545;
            color: white;
            font-size: 0.75rem;
            padding: 2px 8px;
            border-radius: 10px;
            margin-right: 10px;
        }
        
        .preview-badge {
            position: fixed;
            top: 20px;
            left: 20px;
            background: #17a2b8;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: bold;
            z-index: 1000;
            box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            border: none;
            border-radius: 25px;
            padding: 15px 40px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px var(--primary-light);
        }
        
        @media (max-width: 768px) {
            .survey-container {
                margin: 10px;
                border-radius: 15px;
            }
            
            .survey-header {
                padding: 30px 20px;
            }
            
            .survey-body {
                padding: 30px 20px;
            }
            
            .rating-star {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="preview-badge">
        <i class="fas fa-eye me-2"></i>
        وضع المعاينة
    </div>
    
    <div class="survey-container">
        <div class="survey-header">
            <h1 class="mb-3"><?php echo htmlspecialchars($survey['title'] ?? $survey['name']); ?></h1>
            <?php if ($survey['description']): ?>
                <p class="mb-0 opacity-90"><?php echo htmlspecialchars($survey['description']); ?></p>
            <?php endif; ?>
        </div>
        
        <div class="survey-body">
            <?php if (empty($questions)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-question-circle fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد أسئلة في هذا الاستبيان</h5>
                    <p class="text-muted">قم بإضافة أسئلة من صفحة إدارة الأسئلة</p>
                    <a href="survey_questions.php?survey_id=<?php echo $survey['id']; ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إضافة أسئلة
                    </a>
                </div>
            <?php else: ?>
                <form id="surveyForm">
                    <?php foreach ($questions as $index => $question): ?>
                        <div class="question-card">
                            <div class="question-number">
                                <?php echo $question['question_order']; ?>
                            </div>
                            
                            <div class="question-text">
                                <?php echo htmlspecialchars($question['question_text']); ?>
                                <?php if ($question['is_required']): ?>
                                    <span class="required-badge">مطلوب</span>
                                <?php endif; ?>
                            </div>
                            
                            <?php if ($question['question_type'] == 'rating'): ?>
                                <div class="rating-stars" data-question="<?php echo $question['id']; ?>">
                                    <?php for ($i = 1; $i <= 5; $i++): ?>
                                        <i class="fas fa-star rating-star" data-rating="<?php echo $i; ?>"></i>
                                    <?php endfor; ?>
                                </div>
                                <div class="text-center">
                                    <small class="text-muted">انقر على النجوم للتقييم</small>
                                </div>
                            <?php else: ?>
                                <textarea class="form-control" rows="4" 
                                          placeholder="اكتب إجابتك هنا..."
                                          <?php echo $question['is_required'] ? 'required' : ''; ?>></textarea>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>
                    
                    <div class="text-center mt-4">
                        <button type="button" class="btn btn-primary btn-lg" onclick="showPreviewMessage()">
                            <i class="fas fa-paper-plane me-2"></i>
                            إرسال الاستبيان
                        </button>
                    </div>
                </form>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تفعيل تقييم النجوم
        document.querySelectorAll('.rating-stars').forEach(starsContainer => {
            const stars = starsContainer.querySelectorAll('.rating-star');
            
            stars.forEach((star, index) => {
                star.addEventListener('click', () => {
                    const rating = parseInt(star.dataset.rating);
                    
                    // إزالة التفعيل من جميع النجوم
                    stars.forEach(s => s.classList.remove('active'));
                    
                    // تفعيل النجوم حتى النجمة المختارة
                    for (let i = 0; i < rating; i++) {
                        stars[i].classList.add('active');
                    }
                });
                
                star.addEventListener('mouseenter', () => {
                    const rating = parseInt(star.dataset.rating);
                    
                    stars.forEach((s, i) => {
                        if (i < rating) {
                            s.style.color = '#ffc107';
                        } else {
                            s.style.color = s.classList.contains('active') ? '#ffc107' : '#ddd';
                        }
                    });
                });
            });
            
            starsContainer.addEventListener('mouseleave', () => {
                stars.forEach(s => {
                    s.style.color = s.classList.contains('active') ? '#ffc107' : '#ddd';
                });
            });
        });
        
        function showPreviewMessage() {
            alert('هذه معاينة فقط! في الاستبيان الحقيقي سيتم حفظ الإجابات.');
        }
        
        // إضافة تأثيرات بصرية
        document.querySelectorAll('.question-card').forEach((card, index) => {
            card.style.animationDelay = `${index * 0.1}s`;
            card.style.animation = 'fadeInUp 0.6s ease forwards';
        });
        
        // CSS للتأثيرات
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
            
            .question-card {
                opacity: 0;
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
