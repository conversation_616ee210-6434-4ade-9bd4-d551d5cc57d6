<?php
/**
 * إدارة أسئلة الاستبيان
 * Survey Questions Management
 */

define('INCLUDED_FROM_APP', true);
require_once '../config.php';

// التحقق من تسجيل الدخول
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
if (!isset($_SESSION['user_id'])) {
    header("Location: simple_login.php");
    exit();
}

// التحقق من الصلاحيات
$user_role = $_SESSION['user_role'] ?? 'user';
$can_edit = in_array($user_role, ['super_admin', 'manager']);

if (!$can_edit) {
    header("Location: index.php");
    exit();
}

$survey_id = intval($_GET['survey_id'] ?? 1);
$success_message = '';
$error_message = '';

// التحقق من وجود الاستبيان
$stmt = $pdo->prepare("SELECT * FROM surveys WHERE id = ?");
$stmt->execute([$survey_id]);
$survey = $stmt->fetch();

if (!$survey) {
    header("Location: surveys.php");
    exit();
}

$page_title = 'أسئلة الاستبيان: ' . $survey['name'];

// معالجة تعديل سؤال موجود
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['edit_question'])) {
    $question_id = intval($_POST['question_id']);
    $question_text = trim($_POST['question_text'] ?? '');
    $question_type = $_POST['question_type'] ?? 'rating';
    $is_required = isset($_POST['is_required']) ? 1 : 0;

    if (empty($question_text)) {
        $error_message = 'نص السؤال مطلوب';
    } else {
        try {
            $stmt = $pdo->prepare("
                UPDATE survey_questions
                SET question_text = ?, question_type = ?, is_required = ?
                WHERE id = ? AND survey_id = ?
            ");
            $stmt->execute([$question_text, $question_type, $is_required, $question_id, $survey_id]);

            $success_message = 'تم تحديث السؤال بنجاح';
        } catch (Exception $e) {
            $error_message = 'حدث خطأ: ' . $e->getMessage();
        }
    }
}

// معالجة إضافة سؤال جديد
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['add_question'])) {
    $question_text = trim($_POST['question_text'] ?? '');
    $question_type = $_POST['question_type'] ?? 'rating';
    $is_required = isset($_POST['is_required']) ? 1 : 0;
    
    if (empty($question_text)) {
        $error_message = 'نص السؤال مطلوب';
    } else {
        try {
            // الحصول على الترتيب التالي
            $stmt = $pdo->prepare("SELECT COALESCE(MAX(question_order), 0) + 1 as next_order FROM survey_questions WHERE survey_id = ?");
            $stmt->execute([$survey_id]);
            $next_order = $stmt->fetchColumn();
            
            $stmt = $pdo->prepare("
                INSERT INTO survey_questions (survey_id, question_text, question_type, question_order, is_required) 
                VALUES (?, ?, ?, ?, ?)
            ");
            $stmt->execute([$survey_id, $question_text, $question_type, $next_order, $is_required]);
            
            $success_message = 'تم إضافة السؤال بنجاح';
        } catch (Exception $e) {
            $error_message = 'حدث خطأ: ' . $e->getMessage();
        }
    }
}

// معالجة حذف سؤال
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['delete_question'])) {
    $question_id = intval($_POST['question_id']);
    
    try {
        $stmt = $pdo->prepare("DELETE FROM survey_questions WHERE id = ? AND survey_id = ?");
        $stmt->execute([$question_id, $survey_id]);
        
        $success_message = 'تم حذف السؤال بنجاح';
    } catch (Exception $e) {
        $error_message = 'حدث خطأ: ' . $e->getMessage();
    }
}

// معالجة تحديث ترتيب الأسئلة
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['update_order'])) {
    $questions_order = $_POST['questions_order'] ?? [];
    
    try {
        $pdo->beginTransaction();
        
        foreach ($questions_order as $question_id => $order) {
            $stmt = $pdo->prepare("UPDATE survey_questions SET question_order = ? WHERE id = ? AND survey_id = ?");
            $stmt->execute([intval($order), intval($question_id), $survey_id]);
        }
        
        $pdo->commit();
        $success_message = 'تم تحديث ترتيب الأسئلة بنجاح';
    } catch (Exception $e) {
        $pdo->rollBack();
        $error_message = 'حدث خطأ: ' . $e->getMessage();
    }
}

// جلب أسئلة الاستبيان
$stmt = $pdo->prepare("
    SELECT * FROM survey_questions 
    WHERE survey_id = ? 
    ORDER BY question_order ASC
");
$stmt->execute([$survey_id]);
$questions = $stmt->fetchAll();

include 'includes/header.php';
?>

<div class="page-header">
    <h1><i class="fas fa-question-circle me-3"></i><?php echo $page_title; ?></h1>
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="index.php">لوحة المعلومات</a></li>
            <li class="breadcrumb-item"><a href="surveys.php">إدارة الاستبيانات</a></li>
            <li class="breadcrumb-item active">الأسئلة</li>
        </ol>
    </nav>
</div>

<?php if ($success_message): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-check-circle me-2"></i>
        <?php echo htmlspecialchars($success_message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($error_message): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <?php echo htmlspecialchars($error_message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- معلومات الاستبيان -->
<div class="card mb-4" style="border-top: 4px solid <?php echo htmlspecialchars($survey['theme_color']); ?>">
    <div class="card-body">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h5 class="mb-1"><?php echo htmlspecialchars($survey['name']); ?></h5>
                <div class="mb-2">
                    <strong>عنوان للعميل:</strong> <?php echo htmlspecialchars($survey['title'] ?? 'غير محدد'); ?>
                </div>
                <?php if ($survey['description']): ?>
                    <p class="text-muted mb-0 small">
                        <strong>وصف للعميل:</strong> <?php echo htmlspecialchars($survey['description']); ?>
                    </p>
                <?php endif; ?>
            </div>
            <div class="col-md-4 text-end">
                <span class="badge bg-<?php echo $survey['is_active'] ? 'success' : 'secondary'; ?> me-2">
                    <?php echo $survey['is_active'] ? 'نشط' : 'غير نشط'; ?>
                </span>
                <a href="survey_preview.php?survey_id=<?php echo $survey['id']; ?>" 
                   class="btn btn-outline-info btn-sm" target="_blank">
                    <i class="fas fa-eye me-1"></i>
                    معاينة
                </a>
            </div>
        </div>
    </div>
</div>

<!-- إضافة سؤال جديد -->
<div class="card mb-4">
    <div class="card-header">
        <h5><i class="fas fa-plus-circle me-2"></i>إضافة سؤال جديد</h5>
    </div>
    <div class="card-body">
        <form method="POST">
            <div class="row">
                <div class="col-md-8">
                    <div class="mb-3">
                        <label for="question_text" class="form-label">نص السؤال <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="question_text" name="question_text" 
                                  rows="2" required maxlength="1000" 
                                  placeholder="اكتب السؤال هنا..."></textarea>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="question_type" class="form-label">نوع السؤال</label>
                        <select class="form-select" id="question_type" name="question_type">
                            <option value="rating">تقييم بالنجوم</option>
                            <option value="text">نص حر</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_required" name="is_required" checked>
                            <label class="form-check-label" for="is_required">
                                سؤال إجباري
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            
            <button type="submit" name="add_question" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إضافة السؤال
            </button>
        </form>
    </div>
</div>

<!-- قائمة الأسئلة -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5><i class="fas fa-list me-2"></i>الأسئلة الموجودة (<?php echo count($questions); ?>)</h5>
        <?php if (count($questions) > 1): ?>
            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="toggleSortMode()">
                <i class="fas fa-sort me-1"></i>
                إعادة ترتيب
            </button>
        <?php endif; ?>
    </div>
    <div class="card-body">
        <?php if (empty($questions)): ?>
            <div class="text-center py-5">
                <i class="fas fa-question-circle fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد أسئلة</h5>
                <p class="text-muted">قم بإضافة أول سؤال باستخدام النموذج أعلاه</p>
            </div>
        <?php else: ?>
            <form method="POST" id="orderForm" style="display: none;">
                <div class="mb-3">
                    <button type="submit" name="update_order" class="btn btn-success btn-sm">
                        <i class="fas fa-save me-1"></i>
                        حفظ الترتيب
                    </button>
                    <button type="button" class="btn btn-secondary btn-sm" onclick="toggleSortMode()">
                        إلغاء
                    </button>
                </div>
            </form>
            
            <div id="questionsList">
                <?php foreach ($questions as $index => $question): ?>
                    <div class="question-item border rounded p-3 mb-3" data-question-id="<?php echo $question['id']; ?>">
                        <div class="row align-items-center">
                            <div class="col-md-1 text-center sort-handle" style="display: none;">
                                <i class="fas fa-grip-vertical text-muted"></i>
                            </div>
                            
                            <div class="col-md-1 text-center">
                                <span class="badge bg-primary question-order"><?php echo $question['question_order']; ?></span>
                                <input type="hidden" name="questions_order[<?php echo $question['id']; ?>]" 
                                       value="<?php echo $question['question_order']; ?>" form="orderForm">
                            </div>
                            
                            <div class="col-md-7">
                                <div class="question-text">
                                    <?php echo htmlspecialchars($question['question_text']); ?>
                                </div>
                                <div class="question-meta">
                                    <span class="badge bg-<?php echo $question['question_type'] == 'rating' ? 'warning' : 'info'; ?> me-2">
                                        <?php echo $question['question_type'] == 'rating' ? 'تقييم بالنجوم' : 'نص حر'; ?>
                                    </span>
                                    <?php if ($question['is_required']): ?>
                                        <span class="badge bg-danger">إجباري</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            
                            <div class="col-md-3 text-end">
                                <button type="button" class="btn btn-outline-warning btn-sm me-1"
                                        onclick="editQuestion(<?php echo $question['id']; ?>)">
                                    <i class="fas fa-edit"></i>
                                </button>

                                <form method="POST" class="d-inline"
                                      onsubmit="return confirm('هل تريد حذف هذا السؤال؟')">
                                    <input type="hidden" name="question_id" value="<?php echo $question['id']; ?>">
                                    <button type="submit" name="delete_question" class="btn btn-outline-danger btn-sm">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- نافذة تعديل السؤال -->
<div class="modal fade" id="editQuestionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit me-2"></i>
                    تعديل السؤال
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="editQuestionForm">
                <div class="modal-body">
                    <input type="hidden" name="question_id" id="edit_question_id">

                    <div class="mb-3">
                        <label for="edit_question_text" class="form-label">نص السؤال <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="edit_question_text" name="question_text"
                                  rows="3" required maxlength="1000"></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="edit_question_type" class="form-label">نوع السؤال</label>
                        <select class="form-select" id="edit_question_type" name="question_type">
                            <option value="rating">تقييم بالنجوم</option>
                            <option value="text">نص حر</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="edit_is_required" name="is_required">
                            <label class="form-check-label" for="edit_is_required">
                                سؤال إجباري
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>
                        إلغاء
                    </button>
                    <button type="submit" name="edit_question" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>
                        حفظ التغييرات
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.question-item {
    transition: all 0.3s ease;
}

.question-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.question-text {
    font-weight: 500;
    margin-bottom: 8px;
}

.question-meta {
    font-size: 0.875rem;
}

.sort-handle {
    cursor: move;
}

.sortable-ghost {
    opacity: 0.5;
}

.sortable-chosen {
    background-color: #f8f9fa;
}
</style>

<script>
// بيانات الأسئلة للجافا سكريبت
const questionsData = <?php echo json_encode($questions, JSON_UNESCAPED_UNICODE); ?>;

function editQuestion(questionId) {
    // البحث عن السؤال
    const question = questionsData.find(q => q.id == questionId);
    if (!question) {
        alert('لم يتم العثور على السؤال');
        return;
    }

    // ملء النموذج
    document.getElementById('edit_question_id').value = question.id;
    document.getElementById('edit_question_text').value = question.question_text || '';
    document.getElementById('edit_question_type').value = question.question_type || 'rating';
    document.getElementById('edit_is_required').checked = question.is_required == 1;

    // إظهار النافذة
    const modal = new bootstrap.Modal(document.getElementById('editQuestionModal'));
    modal.show();
}

let sortMode = false;

function toggleSortMode() {
    sortMode = !sortMode;
    
    if (sortMode) {
        document.getElementById('orderForm').style.display = 'block';
        document.querySelectorAll('.sort-handle').forEach(el => el.style.display = 'block');
        
        // تفعيل السحب والإفلات
        new Sortable(document.getElementById('questionsList'), {
            handle: '.sort-handle',
            animation: 150,
            ghostClass: 'sortable-ghost',
            chosenClass: 'sortable-chosen',
            onEnd: function(evt) {
                updateOrder();
            }
        });
    } else {
        document.getElementById('orderForm').style.display = 'none';
        document.querySelectorAll('.sort-handle').forEach(el => el.style.display = 'none');
    }
}

function updateOrder() {
    const items = document.querySelectorAll('.question-item');
    items.forEach((item, index) => {
        const questionId = item.dataset.questionId;
        const orderInput = item.querySelector(`input[name="questions_order[${questionId}]"]`);
        const orderBadge = item.querySelector('.question-order');
        
        orderInput.value = index + 1;
        orderBadge.textContent = index + 1;
    });
}
</script>

<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>

<?php include 'includes/footer.php'; ?>
