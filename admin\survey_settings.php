<?php
/**
 * إعدادات الاستبيان
 * Survey Settings
 */

define('INCLUDED_FROM_APP', true);
require_once '../config.php';
require_once '../includes/security.php';

$page_title = 'إدارة الأسئلة';

// التحقق من تسجيل الدخول بشكل مبسط
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
if (!isset($_SESSION['user_id'])) {
    header("Location: simple_login.php");
    exit();
}

// التحقق من الصلاحيات للتعديل
$can_edit = isset($_SESSION['user_role']) && in_array($_SESSION['user_role'], ['super_admin', 'manager']);
$is_readonly = !$can_edit;

$success_message = '';
$error_message = '';

// تحديد الاستبيان الافتراضي
$survey_id = 1;

// جلب بيانات الاستبيان
$stmt = $pdo->prepare("SELECT * FROM surveys WHERE id = ?");
$stmt->execute([$survey_id]);
$survey = $stmt->fetch();

// إذا لم يوجد الاستبيان، إنشاء افتراضي
if (!$survey) {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO surveys (id, name, title, description, thank_you_message, theme_color, is_active, created_by)
            VALUES (1, 'استبيان رضا العملاء الافتراضي', 'استبيان رضا العملاء', 'شكراً لتسوقك معنا نحن متحمسون لسماع رأيك في تجربتك معنا هنا', 'شكراً لك على وقتك في الإجابة على الاستبيان. رأيك مهم جداً لنا ويساعدنا على تحسين خدماتنا.', '#667eea', 1, ?)
        ");
        $stmt->execute([$_SESSION['user_id']]);

        // جلب البيانات مرة أخرى
        $stmt = $pdo->prepare("SELECT * FROM surveys WHERE id = ?");
        $stmt->execute([$survey_id]);
        $survey = $stmt->fetch();
    } catch (Exception $e) {
        // في حالة الخطأ، استخدم بيانات افتراضية
        $survey = [
            'id' => 1,
            'name' => 'استبيان رضا العملاء الافتراضي',
            'title' => 'استبيان رضا العملاء',
            'description' => 'شكراً لتسوقك معنا نحن متحمسون لسماع رأيك في تجربتك معنا هنا',
            'thank_you_message' => 'شكراً لك على وقتك في الإجابة على الاستبيان. رأيك مهم جداً لنا ويساعدنا على تحسين خدماتنا.',
            'theme_color' => '#667eea'
        ];
    }
}

// معالجة إضافة سؤال جديد
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['add_question']) && $can_edit) {
    $question_text = trim($_POST['question_text'] ?? '');
    $question_type = $_POST['question_type'] ?? 'rating';
    $is_required = isset($_POST['is_required']) ? 1 : 0;

    if (empty($question_text)) {
        $error_message = 'نص السؤال مطلوب';
    } else {
        try {
            // الحصول على الترتيب التالي
            $stmt = $pdo->prepare("SELECT COALESCE(MAX(question_order), 0) + 1 as next_order FROM survey_questions WHERE survey_id = ?");
            $stmt->execute([$survey_id]);
            $next_order = $stmt->fetchColumn();

            $stmt = $pdo->prepare("
                INSERT INTO survey_questions (survey_id, question_text, question_type, question_order, is_required)
                VALUES (?, ?, ?, ?, ?)
            ");
            $stmt->execute([$survey_id, $question_text, $question_type, $next_order, $is_required]);

            $success_message = 'تم إضافة السؤال بنجاح';
        } catch (Exception $e) {
            $error_message = 'حدث خطأ: ' . $e->getMessage();
        }
    }
}

// معالجة حذف سؤال
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['delete_question']) && $can_edit) {
    $question_id = intval($_POST['question_id']);

    try {
        $stmt = $pdo->prepare("DELETE FROM survey_questions WHERE id = ? AND survey_id = ?");
        $stmt->execute([$question_id, $survey_id]);

        $success_message = 'تم حذف السؤال بنجاح';
    } catch (Exception $e) {
        $error_message = 'حدث خطأ: ' . $e->getMessage();
    }
}

// معالجة تحديث ترتيب الأسئلة
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['update_order']) && $can_edit) {
    $questions_order = $_POST['questions_order'] ?? [];

    try {
        $pdo->beginTransaction();

        foreach ($questions_order as $question_id => $order) {
            $stmt = $pdo->prepare("UPDATE survey_questions SET question_order = ? WHERE id = ? AND survey_id = ?");
            $stmt->execute([intval($order), intval($question_id), $survey_id]);
        }

        $pdo->commit();
        $success_message = 'تم تحديث ترتيب الأسئلة بنجاح';
    } catch (Exception $e) {
        $pdo->rollBack();
        $error_message = 'حدث خطأ: ' . $e->getMessage();
    }
}

// معالجة تحديث إعدادات الاستبيان العامة (مخفية الآن)
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['update_settings']) && $can_edit) {
    $survey_title = sanitizeInput($_POST['survey_title'] ?? '');
    $survey_description = sanitizeInput($_POST['survey_description'] ?? '');
    $thank_you_message = sanitizeInput($_POST['thank_you_message'] ?? '');
    
    if (empty($survey_title)) {
        $error_message = 'عنوان الاستبيان مطلوب';
    } else {
        try {
            // التحقق من وجود إعدادات
            $stmt = $pdo->query("SELECT id FROM survey_settings LIMIT 1");
            $existing = $stmt->fetch();
            
            if ($existing) {
                $stmt = $pdo->prepare("
                    UPDATE survey_settings 
                    SET survey_title = ?, survey_description = ?, thank_you_message = ?, updated_at = NOW() 
                    WHERE id = ?
                ");
                $stmt->execute([$survey_title, $survey_description, $thank_you_message, $existing['id']]);
            } else {
                $stmt = $pdo->prepare("
                    INSERT INTO survey_settings (survey_title, survey_description, thank_you_message) 
                    VALUES (?, ?, ?)
                ");
                $stmt->execute([$survey_title, $survey_description, $thank_you_message]);
            }
            
            logActivity($_SESSION['user_id'], 'update_survey_settings', 'survey_settings', $existing['id'] ?? $pdo->lastInsertId());
            $success_message = 'تم تحديث إعدادات الاستبيان بنجاح';
            
        } catch (Exception $e) {
            $error_message = $e->getMessage();
        }
    }
}

// معالجة تحديث الأسئلة
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['update_questions']) && $can_edit) {
    try {
        $pdo->beginTransaction();
        
        foreach ($_POST['questions'] as $question_id => $question_data) {
            $question_text = sanitizeInput($question_data['text'] ?? '');
            $is_active = isset($question_data['active']) ? 1 : 0;
            $is_required = isset($question_data['required']) ? 1 : 0;
            $question_order = intval($question_data['order'] ?? 0);
            
            if (!empty($question_text)) {
                $stmt = $pdo->prepare("
                    UPDATE survey_questions 
                    SET question_text = ?, is_active = ?, is_required = ?, question_order = ?, updated_at = NOW() 
                    WHERE id = ?
                ");
                $stmt->execute([$question_text, $is_active, $is_required, $question_order, $question_id]);
            }
        }
        
        $pdo->commit();
        logActivity($_SESSION['user_id'], 'update_questions', 'survey_questions', null);
        $success_message = 'تم تحديث الأسئلة بنجاح';
        
    } catch (Exception $e) {
        $pdo->rollBack();
        $error_message = $e->getMessage();
    }
}

// جلب إعدادات الاستبيان
$stmt = $pdo->query("SELECT * FROM survey_settings LIMIT 1");
$settings = $stmt->fetch();

if (!$settings) {
    $settings = [
        'survey_title' => 'استبيان رضا العملاء',
        'survey_description' => 'شكراً لتسوقك معنا نحن متحمسون لسماع رأيك في تجربتك معنا هنا:',
        'thank_you_message' => 'شكراً لك على وقتك في الإجابة على الاستبيان. رأيك مهم جداً لنا ويساعدنا على تحسين خدماتنا.'
    ];
}

// جلب أسئلة الاستبيان الافتراضي
$stmt = $pdo->prepare("
    SELECT * FROM survey_questions
    WHERE survey_id = ?
    ORDER BY question_order ASC
");
$stmt->execute([$survey_id]);
$questions = $stmt->fetchAll();

include 'includes/header.php';
?>

<div class="page-header">
    <h1><i class="fas fa-cog me-3"></i><?php echo $page_title; ?></h1>
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="index.php">لوحة المعلومات</a></li>
            <li class="breadcrumb-item active">إدارة الأسئلة</li>
        </ol>
    </nav>
</div>

<?php if ($success_message): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-check-circle me-2"></i>
        <?php echo htmlspecialchars($success_message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($error_message): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <?php echo htmlspecialchars($error_message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($is_readonly): ?>
    <div class="alert alert-info alert-dismissible fade show">
        <i class="fas fa-info-circle me-2"></i>
        <strong>وضع القراءة فقط:</strong> يمكنك عرض الأسئلة ولكن لا يمكنك تعديلها. يتطلب التعديل صلاحيات مدير أو مدير عام.
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- معلومات الاستبيان -->
<div class="card mb-4" style="border-top: 4px solid <?php echo htmlspecialchars($survey['theme_color']); ?>">
    <div class="card-body">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h5 class="mb-1"><?php echo htmlspecialchars($survey['name']); ?></h5>
                <div class="mb-2">
                    <strong>عنوان للعميل:</strong> <?php echo htmlspecialchars($survey['title'] ?? 'غير محدد'); ?>
                </div>
                <?php if ($survey['description']): ?>
                    <p class="text-muted mb-0 small">
                        <strong>وصف للعميل:</strong> <?php echo htmlspecialchars($survey['description']); ?>
                    </p>
                <?php endif; ?>
            </div>
            <div class="col-md-4 text-end">
                <span class="badge bg-<?php echo $survey['is_active'] ? 'success' : 'secondary'; ?> me-2">
                    <?php echo $survey['is_active'] ? 'نشط' : 'غير نشط'; ?>
                </span>
                <a href="../index.php?survey_id=<?php echo $survey['id']; ?>&inv=TEST123&le=01&br=0101&sm=ID0003&cp=0501234567"
                   class="btn btn-outline-info btn-sm" target="_blank">
                    <i class="fas fa-eye me-1"></i>
                    معاينة
                </a>
                <a href="surveys.php" class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-poll me-1"></i>
                    إدارة الاستبيانات
                </a>
            </div>
        </div>
    </div>
</div>

<!-- إضافة سؤال جديد -->
<?php if (!$is_readonly): ?>
<div class="card mb-4">
    <div class="card-header">
        <h5><i class="fas fa-plus-circle me-2"></i>إضافة سؤال جديد</h5>
    </div>
    <div class="card-body">
        <form method="POST">
            <div class="row">
                <div class="col-md-8">
                    <div class="mb-3">
                        <label for="question_text" class="form-label">نص السؤال <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="question_text" name="question_text"
                                  rows="2" required maxlength="1000"
                                  placeholder="اكتب السؤال هنا..."></textarea>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="question_type" class="form-label">نوع السؤال</label>
                        <select class="form-select" id="question_type" name="question_type">
                            <option value="rating">تقييم بالنجوم</option>
                            <option value="text">نص حر</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_required" name="is_required" checked>
                            <label class="form-check-label" for="is_required">
                                سؤال إجباري
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <button type="submit" name="add_question" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إضافة السؤال
            </button>
        </form>
    </div>
</div>
<?php endif; ?>

<!-- قائمة الأسئلة -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5><i class="fas fa-list me-2"></i>الأسئلة الموجودة (<?php echo count($questions); ?>)</h5>
        <?php if (count($questions) > 1 && !$is_readonly): ?>
            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="toggleSortMode()">
                <i class="fas fa-sort me-1"></i>
                إعادة ترتيب
            </button>
        <?php endif; ?>
    </div>
    <div class="card-body">
        <?php if (empty($questions)): ?>
            <div class="text-center py-5">
                <i class="fas fa-question-circle fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد أسئلة</h5>
                <p class="text-muted">قم بإضافة أول سؤال باستخدام النموذج أعلاه</p>
            </div>
        <?php else: ?>
            <?php if (!$is_readonly): ?>
                <form method="POST" id="orderForm" style="display: none;">
                    <div class="mb-3">
                        <button type="submit" name="update_order" class="btn btn-success btn-sm">
                            <i class="fas fa-save me-1"></i>
                            حفظ الترتيب
                        </button>
                        <button type="button" class="btn btn-secondary btn-sm" onclick="toggleSortMode()">
                            إلغاء
                        </button>
                    </div>
                </form>
            <?php endif; ?>

            <div id="questionsList">
                <?php foreach ($questions as $index => $question): ?>
                    <div class="question-item border rounded p-3 mb-3" data-question-id="<?php echo $question['id']; ?>">
                        <div class="row align-items-center">
                            <?php if (!$is_readonly): ?>
                                <div class="col-md-1 text-center sort-handle" style="display: none;">
                                    <i class="fas fa-grip-vertical text-muted"></i>
                                </div>
                            <?php endif; ?>

                            <div class="col-md-1 text-center">
                                <span class="badge bg-primary question-order"><?php echo $question['question_order']; ?></span>
                                <?php if (!$is_readonly): ?>
                                    <input type="hidden" name="questions_order[<?php echo $question['id']; ?>]"
                                           value="<?php echo $question['question_order']; ?>" form="orderForm">
                                <?php endif; ?>
                            </div>

                            <div class="col-md-<?php echo $is_readonly ? '8' : '7'; ?>">
                                <div class="question-text">
                                    <?php echo htmlspecialchars($question['question_text']); ?>
                                </div>
                                <div class="question-meta">
                                    <span class="badge bg-<?php echo $question['question_type'] == 'rating' ? 'warning' : 'info'; ?> me-2">
                                        <?php echo $question['question_type'] == 'rating' ? 'تقييم بالنجوم' : 'نص حر'; ?>
                                    </span>
                                    <?php if ($question['is_required']): ?>
                                        <span class="badge bg-danger">إجباري</span>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <?php if (!$is_readonly): ?>
                                <div class="col-md-3 text-end">
                                    <form method="POST" class="d-inline"
                                          onsubmit="return confirm('هل تريد حذف هذا السؤال؟')">
                                        <input type="hidden" name="question_id" value="<?php echo $question['id']; ?>">
                                        <button type="submit" name="delete_question" class="btn btn-outline-danger btn-sm">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- معاينة الاستبيان -->
<div class="card mt-4">
    <div class="card-header">
        <h5><i class="fas fa-eye me-2"></i>معاينة الاستبيان</h5>
    </div>
    <div class="card-body">
        <div class="text-center mb-3">
            <a href="../index.php?inv=TEST123&le=01&br=0101&sm=ID0003&cp=0501234567" 
               target="_blank" class="btn btn-outline-primary">
                <i class="fas fa-external-link-alt me-2"></i>
                فتح الاستبيان في نافذة جديدة
            </a>
        </div>
        
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            <strong>ملاحظة:</strong> الرابط أعلاه للمعاينة فقط ويحتوي على بيانات تجريبية. 
            لن يتم حفظ الردود من هذا الرابط.
        </div>
    </div>
</div>

<?php
$extra_js = "
<script src='https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js'></script>
<script>
let sortMode = false;

function toggleSortMode() {
    sortMode = !sortMode;

    if (sortMode) {
        document.getElementById('orderForm').style.display = 'block';
        document.querySelectorAll('.sort-handle').forEach(el => el.style.display = 'block');

        // تفعيل السحب والإفلات
        new Sortable(document.getElementById('questionsList'), {
            handle: '.sort-handle',
            animation: 150,
            ghostClass: 'sortable-ghost',
            chosenClass: 'sortable-chosen',
            onEnd: function(evt) {
                updateOrder();
            }
        });
    } else {
        document.getElementById('orderForm').style.display = 'none';
        document.querySelectorAll('.sort-handle').forEach(el => el.style.display = 'none');
    }
}

function updateOrder() {
    const items = document.querySelectorAll('.question-item');
    items.forEach((item, index) => {
        const questionId = item.dataset.questionId;
        const orderInput = item.querySelector(`input[name=\"questions_order[${questionId}]\"]`);
        const orderBadge = item.querySelector('.question-order');

        if (orderInput && orderBadge) {
            orderInput.value = index + 1;
            orderBadge.textContent = index + 1;
        }
    });
}
</script>
<style>
.question-item {
    transition: all 0.3s ease;
}

.question-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.question-text {
    font-weight: 500;
    margin-bottom: 8px;
}

.question-meta {
    font-size: 0.875rem;
}

.sort-handle {
    cursor: move;
}

.sortable-ghost {
    opacity: 0.5;
}

.sortable-chosen {
    background-color: #f8f9fa;
}
</style>
";

include 'includes/footer.php';
?>
