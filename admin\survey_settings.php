<?php
/**
 * إعدادات الاستبيان
 * Survey Settings
 */

define('INCLUDED_FROM_APP', true);
require_once '../config.php';
require_once '../includes/security.php';

$page_title = 'إعدادات الاستبيان';

// التحقق من تسجيل الدخول بشكل مبسط
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
if (!isset($_SESSION['user_id'])) {
    header("Location: simple_login.php");
    exit();
}

// التحقق من الصلاحيات للتعديل
$can_edit = isset($_SESSION['user_role']) && in_array($_SESSION['user_role'], ['super_admin', 'manager']);
$is_readonly = !$can_edit;

$success_message = '';
$error_message = '';

// معالجة تحديث إعدادات الاستبيان العامة
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['update_settings']) && $can_edit) {
    $survey_title = sanitizeInput($_POST['survey_title'] ?? '');
    $survey_description = sanitizeInput($_POST['survey_description'] ?? '');
    $thank_you_message = sanitizeInput($_POST['thank_you_message'] ?? '');
    
    if (empty($survey_title)) {
        $error_message = 'عنوان الاستبيان مطلوب';
    } else {
        try {
            // التحقق من وجود إعدادات
            $stmt = $pdo->query("SELECT id FROM survey_settings LIMIT 1");
            $existing = $stmt->fetch();
            
            if ($existing) {
                $stmt = $pdo->prepare("
                    UPDATE survey_settings 
                    SET survey_title = ?, survey_description = ?, thank_you_message = ?, updated_at = NOW() 
                    WHERE id = ?
                ");
                $stmt->execute([$survey_title, $survey_description, $thank_you_message, $existing['id']]);
            } else {
                $stmt = $pdo->prepare("
                    INSERT INTO survey_settings (survey_title, survey_description, thank_you_message) 
                    VALUES (?, ?, ?)
                ");
                $stmt->execute([$survey_title, $survey_description, $thank_you_message]);
            }
            
            logActivity($_SESSION['user_id'], 'update_survey_settings', 'survey_settings', $existing['id'] ?? $pdo->lastInsertId());
            $success_message = 'تم تحديث إعدادات الاستبيان بنجاح';
            
        } catch (Exception $e) {
            $error_message = $e->getMessage();
        }
    }
}

// معالجة تحديث الأسئلة
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['update_questions']) && $can_edit) {
    try {
        $pdo->beginTransaction();
        
        foreach ($_POST['questions'] as $question_id => $question_data) {
            $question_text = sanitizeInput($question_data['text'] ?? '');
            $is_active = isset($question_data['active']) ? 1 : 0;
            $is_required = isset($question_data['required']) ? 1 : 0;
            $question_order = intval($question_data['order'] ?? 0);
            
            if (!empty($question_text)) {
                $stmt = $pdo->prepare("
                    UPDATE survey_questions 
                    SET question_text = ?, is_active = ?, is_required = ?, question_order = ?, updated_at = NOW() 
                    WHERE id = ?
                ");
                $stmt->execute([$question_text, $is_active, $is_required, $question_order, $question_id]);
            }
        }
        
        $pdo->commit();
        logActivity($_SESSION['user_id'], 'update_questions', 'survey_questions', null);
        $success_message = 'تم تحديث الأسئلة بنجاح';
        
    } catch (Exception $e) {
        $pdo->rollBack();
        $error_message = $e->getMessage();
    }
}

// جلب إعدادات الاستبيان
$stmt = $pdo->query("SELECT * FROM survey_settings LIMIT 1");
$settings = $stmt->fetch();

if (!$settings) {
    $settings = [
        'survey_title' => 'استبيان رضا العملاء',
        'survey_description' => 'شكراً لتسوقك معنا نحن متحمسون لسماع رأيك في تجربتك معنا هنا:',
        'thank_you_message' => 'شكراً لك على وقتك في الإجابة على الاستبيان. رأيك مهم جداً لنا ويساعدنا على تحسين خدماتنا.'
    ];
}

// جلب الأسئلة
$stmt = $pdo->query("SELECT * FROM survey_questions ORDER BY question_order, id");
$questions = $stmt->fetchAll();

include 'includes/header.php';
?>

<div class="page-header">
    <h1><i class="fas fa-cog me-3"></i><?php echo $page_title; ?></h1>
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="index.php">لوحة المعلومات</a></li>
            <li class="breadcrumb-item active">إعدادات الاستبيان</li>
        </ol>
    </nav>
</div>

<?php if ($success_message): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-check-circle me-2"></i>
        <?php echo htmlspecialchars($success_message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($error_message): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <?php echo htmlspecialchars($error_message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($is_readonly): ?>
    <div class="alert alert-info alert-dismissible fade show">
        <i class="fas fa-info-circle me-2"></i>
        <strong>وضع القراءة فقط:</strong> يمكنك عرض الإعدادات ولكن لا يمكنك تعديلها. يتطلب التعديل صلاحيات مدير أو مدير عام.
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- إعدادات الاستبيان العامة -->
<div class="card mb-4">
    <div class="card-header">
        <h5><i class="fas fa-edit me-2"></i>الإعدادات العامة</h5>
    </div>
    <div class="card-body">
        <form method="POST">
            <div class="row">
                <div class="col-md-12">
                    <div class="mb-3">
                        <label for="survey_title" class="form-label">عنوان الاستبيان <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="survey_title" name="survey_title"
                               value="<?php echo htmlspecialchars($settings['survey_title']); ?>"
                               <?php echo $is_readonly ? 'readonly' : 'required'; ?>>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-12">
                    <div class="mb-3">
                        <label for="survey_description" class="form-label">وصف الاستبيان</label>
                        <textarea class="form-control" id="survey_description" name="survey_description" rows="3"
                                  maxlength="500" <?php echo $is_readonly ? 'readonly' : ''; ?>><?php echo htmlspecialchars($settings['survey_description']); ?></textarea>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-12">
                    <div class="mb-3">
                        <label for="thank_you_message" class="form-label">رسالة الشكر</label>
                        <textarea class="form-control" id="thank_you_message" name="thank_you_message" rows="3" 
                                  maxlength="500"><?php echo htmlspecialchars($settings['thank_you_message']); ?></textarea>
                    </div>
                </div>
            </div>
            
            <div class="text-end">
                <button type="submit" name="update_settings" class="btn btn-primary">
                    <i class="fas fa-save me-2"></i>
                    حفظ الإعدادات
                </button>
            </div>
        </form>
    </div>
</div>

<!-- إدارة الأسئلة -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5><i class="fas fa-question-circle me-2"></i>إدارة الأسئلة</h5>
        <a href="add_question.php" class="btn btn-success btn-sm">
            <i class="fas fa-plus me-2"></i>
            إضافة سؤال جديد
        </a>
    </div>
    <div class="card-body">
        <form method="POST" id="questionsForm">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th width="50">الترتيب</th>
                            <th>نص السؤال</th>
                            <th width="100">النوع</th>
                            <th width="80">نشط</th>
                            <th width="80">مطلوب</th>
                        </tr>
                    </thead>
                    <tbody id="questionsTable">
                        <?php foreach ($questions as $index => $question): ?>
                            <tr>
                                <td>
                                    <input type="number" class="form-control form-control-sm" 
                                           name="questions[<?php echo $question['id']; ?>][order]" 
                                           value="<?php echo $question['question_order']; ?>" min="1" max="100">
                                </td>
                                <td>
                                    <textarea class="form-control form-control-sm" 
                                              name="questions[<?php echo $question['id']; ?>][text]" 
                                              rows="2" maxlength="500" required><?php echo htmlspecialchars($question['question_text']); ?></textarea>
                                </td>
                                <td>
                                    <span class="badge bg-<?php echo $question['question_type'] == 'rating' ? 'warning' : 'info'; ?>">
                                        <?php echo $question['question_type'] == 'rating' ? 'تقييم' : 'نص'; ?>
                                    </span>
                                </td>
                                <td>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" 
                                               name="questions[<?php echo $question['id']; ?>][active]" 
                                               <?php echo $question['is_active'] ? 'checked' : ''; ?>>
                                    </div>
                                </td>
                                <td>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" 
                                               name="questions[<?php echo $question['id']; ?>][required]" 
                                               <?php echo $question['is_required'] ? 'checked' : ''; ?>>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <div class="text-end mt-3">
                <button type="submit" name="update_questions" class="btn btn-success">
                    <i class="fas fa-save me-2"></i>
                    حفظ الأسئلة
                </button>
            </div>
        </form>
    </div>
</div>

<!-- معاينة الاستبيان -->
<div class="card mt-4">
    <div class="card-header">
        <h5><i class="fas fa-eye me-2"></i>معاينة الاستبيان</h5>
    </div>
    <div class="card-body">
        <div class="text-center mb-3">
            <a href="../index.php?inv=TEST123&le=01&br=0101&sm=ID0003&cp=0501234567" 
               target="_blank" class="btn btn-outline-primary">
                <i class="fas fa-external-link-alt me-2"></i>
                فتح الاستبيان في نافذة جديدة
            </a>
        </div>
        
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            <strong>ملاحظة:</strong> الرابط أعلاه للمعاينة فقط ويحتوي على بيانات تجريبية. 
            لن يتم حفظ الردود من هذا الرابط.
        </div>
    </div>
</div>

<?php
$extra_js = "
<script>
// تأكيد حفظ التغييرات
document.getElementById('questionsForm').addEventListener('submit', function(e) {
    if (!confirm('هل أنت متأكد من حفظ التغييرات؟')) {
        e.preventDefault();
    }
});

// تحديث ترقيم الأسئلة تلقائياً
document.querySelectorAll('input[name*=\"[order]\"]').forEach(function(input, index) {
    input.addEventListener('change', function() {
        // التأكد من عدم تكرار الأرقام
        const allOrders = Array.from(document.querySelectorAll('input[name*=\"[order]\"]'))
                              .map(inp => parseInt(inp.value))
                              .filter(val => !isNaN(val));
        
        const duplicates = allOrders.filter((item, index) => allOrders.indexOf(item) !== index);
        
        if (duplicates.length > 0) {
            alert('يوجد تكرار في أرقام الترتيب. يرجى التأكد من عدم تكرار الأرقام.');
            this.focus();
        }
    });
});
</script>
";

include 'includes/footer.php';
?>
