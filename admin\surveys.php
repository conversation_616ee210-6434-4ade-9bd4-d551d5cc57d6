<?php
/**
 * إدارة الاستبيانات المتعددة
 * Multiple Surveys Management
 */

define('INCLUDED_FROM_APP', true);
require_once '../config.php';

// التحقق من تسجيل الدخول
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
if (!isset($_SESSION['user_id'])) {
    header("Location: simple_login.php");
    exit();
}

// التحقق من الصلاحيات
$user_role = $_SESSION['user_role'] ?? 'user';
$can_edit = in_array($user_role, ['super_admin', 'manager']);

if (!$can_edit) {
    header("Location: index.php");
    exit();
}

$page_title = 'إدارة الاستبيانات';
$success_message = '';
$error_message = '';

// معالجة إضافة استبيان جديد
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['create_survey'])) {
    $survey_name = trim($_POST['survey_name'] ?? '');
    $survey_description = trim($_POST['survey_description'] ?? '');
    $theme_color = $_POST['theme_color'] ?? '#667eea';
    $is_active = isset($_POST['is_active']) ? 1 : 0;
    
    if (empty($survey_name)) {
        $error_message = 'اسم الاستبيان مطلوب';
    } else {
        try {
            $stmt = $pdo->prepare("
                INSERT INTO surveys (name, description, theme_color, is_active, created_by) 
                VALUES (?, ?, ?, ?, ?)
            ");
            $stmt->execute([$survey_name, $survey_description, $theme_color, $is_active, $_SESSION['user_id']]);
            
            $success_message = 'تم إنشاء الاستبيان بنجاح';
        } catch (Exception $e) {
            $error_message = 'حدث خطأ: ' . $e->getMessage();
        }
    }
}

// معالجة تحديث حالة الاستبيان
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['toggle_status'])) {
    $survey_id = intval($_POST['survey_id']);
    $new_status = intval($_POST['new_status']);
    
    try {
        $stmt = $pdo->prepare("UPDATE surveys SET is_active = ? WHERE id = ?");
        $stmt->execute([$new_status, $survey_id]);
        
        $success_message = 'تم تحديث حالة الاستبيان';
    } catch (Exception $e) {
        $error_message = 'حدث خطأ: ' . $e->getMessage();
    }
}

// جلب جميع الاستبيانات
$stmt = $pdo->query("
    SELECT s.*, u.full_name as creator_name,
           (SELECT COUNT(*) FROM survey_questions sq WHERE sq.survey_id = s.id) as questions_count,
           (SELECT COUNT(*) FROM survey_responses sr WHERE sr.survey_id = s.id) as responses_count
    FROM surveys s
    LEFT JOIN users u ON s.created_by = u.id
    ORDER BY s.created_at DESC
");
$surveys = $stmt->fetchAll();

include 'includes/header.php';
?>

<div class="page-header">
    <h1><i class="fas fa-poll me-3"></i><?php echo $page_title; ?></h1>
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="index.php">لوحة المعلومات</a></li>
            <li class="breadcrumb-item active">إدارة الاستبيانات</li>
        </ol>
    </nav>
</div>

<?php if ($success_message): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-check-circle me-2"></i>
        <?php echo htmlspecialchars($success_message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($error_message): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <?php echo htmlspecialchars($error_message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- إنشاء استبيان جديد -->
<div class="card mb-4">
    <div class="card-header">
        <h5><i class="fas fa-plus-circle me-2"></i>إنشاء استبيان جديد</h5>
    </div>
    <div class="card-body">
        <form method="POST">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="survey_name" class="form-label">اسم الاستبيان <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="survey_name" name="survey_name" 
                               required maxlength="255" placeholder="مثال: استبيان رضا العملاء">
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="theme_color" class="form-label">لون الثيم</label>
                        <input type="color" class="form-control form-control-color" id="theme_color" 
                               name="theme_color" value="#667eea">
                    </div>
                </div>
            </div>
            
            <div class="mb-3">
                <label for="survey_description" class="form-label">وصف الاستبيان</label>
                <textarea class="form-control" id="survey_description" name="survey_description" 
                          rows="3" maxlength="500" placeholder="وصف مختصر للاستبيان..."></textarea>
            </div>
            
            <div class="mb-3">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                    <label class="form-check-label" for="is_active">
                        <i class="fas fa-eye me-1"></i>
                        نشط (متاح للعملاء)
                    </label>
                </div>
            </div>
            
            <button type="submit" name="create_survey" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إنشاء الاستبيان
            </button>
        </form>
    </div>
</div>

<!-- قائمة الاستبيانات -->
<div class="card">
    <div class="card-header">
        <h5><i class="fas fa-list me-2"></i>الاستبيانات الموجودة (<?php echo count($surveys); ?>)</h5>
    </div>
    <div class="card-body">
        <?php if (empty($surveys)): ?>
            <div class="text-center py-5">
                <i class="fas fa-poll fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد استبيانات</h5>
                <p class="text-muted">قم بإنشاء أول استبيان باستخدام النموذج أعلاه</p>
            </div>
        <?php else: ?>
            <div class="row">
                <?php foreach ($surveys as $survey): ?>
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="card h-100 survey-card" style="border-top: 4px solid <?php echo htmlspecialchars($survey['theme_color']); ?>">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start mb-3">
                                    <h6 class="card-title mb-0"><?php echo htmlspecialchars($survey['name']); ?></h6>
                                    <span class="badge bg-<?php echo $survey['is_active'] ? 'success' : 'secondary'; ?>">
                                        <?php echo $survey['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                    </span>
                                </div>
                                
                                <?php if ($survey['description']): ?>
                                    <p class="card-text text-muted small">
                                        <?php echo htmlspecialchars($survey['description']); ?>
                                    </p>
                                <?php endif; ?>
                                
                                <div class="row text-center mb-3">
                                    <div class="col-6">
                                        <div class="border-end">
                                            <h6 class="mb-0"><?php echo $survey['questions_count']; ?></h6>
                                            <small class="text-muted">سؤال</small>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <h6 class="mb-0"><?php echo $survey['responses_count']; ?></h6>
                                        <small class="text-muted">رد</small>
                                    </div>
                                </div>
                                
                                <div class="d-flex gap-2">
                                    <a href="survey_questions.php?survey_id=<?php echo $survey['id']; ?>" 
                                       class="btn btn-sm btn-outline-primary flex-fill">
                                        <i class="fas fa-question-circle me-1"></i>
                                        الأسئلة
                                    </a>
                                    
                                    <a href="survey_preview.php?survey_id=<?php echo $survey['id']; ?>" 
                                       class="btn btn-sm btn-outline-info flex-fill" target="_blank">
                                        <i class="fas fa-eye me-1"></i>
                                        معاينة
                                    </a>
                                    
                                    <form method="POST" class="d-inline">
                                        <input type="hidden" name="survey_id" value="<?php echo $survey['id']; ?>">
                                        <input type="hidden" name="new_status" value="<?php echo $survey['is_active'] ? 0 : 1; ?>">
                                        <button type="submit" name="toggle_status" 
                                                class="btn btn-sm btn-outline-<?php echo $survey['is_active'] ? 'warning' : 'success'; ?>"
                                                onclick="return confirm('هل تريد تغيير حالة الاستبيان؟')">
                                            <i class="fas fa-<?php echo $survey['is_active'] ? 'eye-slash' : 'eye'; ?>"></i>
                                        </button>
                                    </form>
                                </div>
                            </div>
                            
                            <div class="card-footer bg-light">
                                <small class="text-muted">
                                    <i class="fas fa-user me-1"></i>
                                    <?php echo htmlspecialchars($survey['creator_name'] ?? 'غير محدد'); ?>
                                    <span class="float-end">
                                        <i class="fas fa-calendar me-1"></i>
                                        <?php echo date('Y-m-d', strtotime($survey['created_at'])); ?>
                                    </span>
                                </small>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<style>
.survey-card {
    transition: transform 0.2s, box-shadow 0.2s;
}

.survey-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.form-control-color {
    width: 60px;
    height: 38px;
    padding: 0;
    border: 1px solid #ced4da;
}
</style>

<?php include 'includes/footer.php'; ?>
