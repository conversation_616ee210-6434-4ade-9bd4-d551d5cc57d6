<?php
/**
 * إدارة المستخدمين
 * Users Management
 */

define('INCLUDED_FROM_APP', true);
require_once '../config.php';
require_once '../includes/security.php';

$page_title = 'إدارة المستخدمين';

// التحقق من الصلاحيات
if (!hasPermission('manager')) {
    showMessage('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error');
    redirect('index.php');
}

$success_message = '';
$error_message = '';

// معالجة إضافة مستخدم جديد
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['add_user'])) {
    $username = sanitizeInput($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $full_name = sanitizeInput($_POST['full_name'] ?? '');
    $email = sanitizeInput($_POST['email'] ?? '');
    $phone = sanitizeInput($_POST['phone'] ?? '');
    $role = $_POST['role'] ?? 'user';
    $is_active = isset($_POST['is_active']) ? 1 : 0;
    
    if (empty($username) || empty($password) || empty($full_name)) {
        $error_message = 'اسم المستخدم وكلمة المرور والاسم الكامل مطلوبة';
    } elseif (!validatePassword($password)) {
        $error_message = 'كلمة المرور يجب أن تحتوي على 8 أحرف على الأقل وتشمل حروف كبيرة وصغيرة وأرقام';
    } elseif (!empty($email) && !validateEmail($email)) {
        $error_message = 'البريد الإلكتروني غير صحيح';
    } else {
        try {
            // التحقق من عدم تكرار اسم المستخدم
            $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ? AND is_deleted = 0");
            $stmt->execute([$username]);
            if ($stmt->fetch()) {
                throw new Exception('اسم المستخدم موجود مسبقاً');
            }
            
            // التحقق من عدم تكرار البريد الإلكتروني
            if (!empty($email)) {
                $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ? AND is_deleted = 0");
                $stmt->execute([$email]);
                if ($stmt->fetch()) {
                    throw new Exception('البريد الإلكتروني مستخدم مسبقاً');
                }
            }
            
            $hashed_password = hashPassword($password);
            
            $stmt = $pdo->prepare("
                INSERT INTO users (username, password, full_name, email, phone, role, is_active) 
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([$username, $hashed_password, $full_name, $email ?: null, $phone ?: null, $role, $is_active]);
            
            $new_user_id = $pdo->lastInsertId();
            logActivity($_SESSION['user_id'], 'create_user', 'users', $new_user_id);
            
            $success_message = 'تم إضافة المستخدم بنجاح';
            
        } catch (Exception $e) {
            $error_message = $e->getMessage();
        }
    }
}

// معالجة تحديث المستخدم
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['update_user'])) {
    $user_id = intval($_POST['user_id']);
    $full_name = sanitizeInput($_POST['full_name'] ?? '');
    $email = sanitizeInput($_POST['email'] ?? '');
    $phone = sanitizeInput($_POST['phone'] ?? '');
    $role = $_POST['role'] ?? 'user';
    $is_active = isset($_POST['is_active']) ? 1 : 0;
    
    if (empty($full_name)) {
        $error_message = 'الاسم الكامل مطلوب';
    } elseif (!empty($email) && !validateEmail($email)) {
        $error_message = 'البريد الإلكتروني غير صحيح';
    } else {
        try {
            // التحقق من وجود المستخدم
            $stmt = $pdo->prepare("SELECT username, role FROM users WHERE id = ? AND is_deleted = 0");
            $stmt->execute([$user_id]);
            $existing_user = $stmt->fetch();
            
            if (!$existing_user) {
                throw new Exception('المستخدم غير موجود');
            }
            
            // منع تعديل المستخدم الرئيسي
            if ($existing_user['username'] == 'Admin' && $_SESSION['username'] != 'Admin') {
                throw new Exception('لا يمكن تعديل المستخدم الرئيسي');
            }
            
            // منع المستخدم من تعديل صلاحياته الخاصة
            if ($user_id == $_SESSION['user_id'] && $role != $existing_user['role']) {
                throw new Exception('لا يمكنك تعديل صلاحياتك الخاصة');
            }
            
            // التحقق من عدم تكرار البريد الإلكتروني
            if (!empty($email)) {
                $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ? AND id != ? AND is_deleted = 0");
                $stmt->execute([$email, $user_id]);
                if ($stmt->fetch()) {
                    throw new Exception('البريد الإلكتروني مستخدم من قبل مستخدم آخر');
                }
            }
            
            $stmt = $pdo->prepare("
                UPDATE users 
                SET full_name = ?, email = ?, phone = ?, role = ?, is_active = ?, updated_at = NOW() 
                WHERE id = ?
            ");
            $stmt->execute([$full_name, $email ?: null, $phone ?: null, $role, $is_active, $user_id]);
            
            logActivity($_SESSION['user_id'], 'update_user', 'users', $user_id);
            $success_message = 'تم تحديث المستخدم بنجاح';
            
        } catch (Exception $e) {
            $error_message = $e->getMessage();
        }
    }
}

// معالجة حذف المستخدم (حذف آمن)
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['delete_user'])) {
    $user_id = intval($_POST['user_id']);
    
    try {
        // التحقق من وجود المستخدم
        $stmt = $pdo->prepare("SELECT username FROM users WHERE id = ? AND is_deleted = 0");
        $stmt->execute([$user_id]);
        $existing_user = $stmt->fetch();
        
        if (!$existing_user) {
            throw new Exception('المستخدم غير موجود');
        }
        
        // منع حذف المستخدم الرئيسي
        if ($existing_user['username'] == 'Admin') {
            throw new Exception('لا يمكن حذف المستخدم الرئيسي');
        }
        
        // منع المستخدم من حذف نفسه
        if ($user_id == $_SESSION['user_id']) {
            throw new Exception('لا يمكنك حذف حسابك الخاص');
        }
        
        // حذف آمن
        $stmt = $pdo->prepare("UPDATE users SET is_deleted = 1, updated_at = NOW() WHERE id = ?");
        $stmt->execute([$user_id]);
        
        logActivity($_SESSION['user_id'], 'delete_user', 'users', $user_id);
        $success_message = 'تم حذف المستخدم بنجاح';
        
    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}

// جلب المستخدمين
$stmt = $pdo->query("
    SELECT * FROM users 
    WHERE is_deleted = 0 
    ORDER BY created_at DESC
");
$users = $stmt->fetchAll();

include 'includes/header.php';
?>

<div class="page-header">
    <h1><i class="fas fa-users me-3"></i><?php echo $page_title; ?></h1>
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="index.php">لوحة المعلومات</a></li>
            <li class="breadcrumb-item active">إدارة المستخدمين</li>
        </ol>
    </nav>
</div>

<?php if ($success_message): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-check-circle me-2"></i>
        <?php echo htmlspecialchars($success_message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($error_message): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <?php echo htmlspecialchars($error_message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- إضافة مستخدم جديد -->
<?php if (hasPermission('super_admin')): ?>
<div class="card mb-4">
    <div class="card-header">
        <h5><i class="fas fa-user-plus me-2"></i>إضافة مستخدم جديد</h5>
    </div>
    <div class="card-body">
        <form method="POST" class="row g-3">
            <div class="col-md-3">
                <label for="username" class="form-label">اسم المستخدم <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="username" name="username" required>
            </div>
            
            <div class="col-md-3">
                <label for="password" class="form-label">كلمة المرور <span class="text-danger">*</span></label>
                <input type="password" class="form-control" id="password" name="password" required>
            </div>
            
            <div class="col-md-3">
                <label for="full_name" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="full_name" name="full_name" required>
            </div>
            
            <div class="col-md-3">
                <label for="email" class="form-label">البريد الإلكتروني</label>
                <input type="email" class="form-control" id="email" name="email">
            </div>
            
            <div class="col-md-3">
                <label for="phone" class="form-label">رقم الجوال</label>
                <input type="tel" class="form-control" id="phone" name="phone">
            </div>
            
            <div class="col-md-3">
                <label for="role" class="form-label">الصلاحية</label>
                <select class="form-select" id="role" name="role">
                    <option value="user">مستخدم</option>
                    <option value="manager">مدير</option>
                    <option value="super_admin">مدير عام</option>
                </select>
            </div>
            
            <div class="col-md-3">
                <div class="form-check mt-4">
                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                    <label class="form-check-label" for="is_active">
                        نشط
                    </label>
                </div>
            </div>
            
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" name="add_user" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    إضافة المستخدم
                </button>
            </div>
        </form>
    </div>
</div>
<?php endif; ?>

<!-- قائمة المستخدمين -->
<div class="card">
    <div class="card-header">
        <h5><i class="fas fa-list me-2"></i>قائمة المستخدمين (<?php echo count($users); ?>)</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped" id="usersTable">
                <thead>
                    <tr>
                        <th>اسم المستخدم</th>
                        <th>الاسم الكامل</th>
                        <th>البريد الإلكتروني</th>
                        <th>الصلاحية</th>
                        <th>الحالة</th>
                        <th>تاريخ الإنشاء</th>
                        <th>آخر دخول</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($users as $user): ?>
                        <tr>
                            <td>
                                <strong><?php echo htmlspecialchars($user['username']); ?></strong>
                                <?php if ($user['username'] == 'Admin'): ?>
                                    <span class="badge bg-danger ms-2">رئيسي</span>
                                <?php endif; ?>
                            </td>
                            <td><?php echo htmlspecialchars($user['full_name']); ?></td>
                            <td><?php echo htmlspecialchars($user['email'] ?? 'غير محدد'); ?></td>
                            <td>
                                <span class="badge bg-<?php echo $user['role'] == 'super_admin' ? 'danger' : ($user['role'] == 'manager' ? 'warning' : 'info'); ?>">
                                    <?php 
                                    $roles = [
                                        'super_admin' => 'مدير عام',
                                        'manager' => 'مدير',
                                        'user' => 'مستخدم'
                                    ];
                                    echo $roles[$user['role']] ?? $user['role'];
                                    ?>
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-<?php echo $user['is_active'] ? 'success' : 'secondary'; ?>">
                                    <?php echo $user['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                </span>
                            </td>
                            <td><?php echo date('Y-m-d', strtotime($user['created_at'])); ?></td>
                            <td><?php echo $user['last_login'] ? date('Y-m-d H:i', strtotime($user['last_login'])) : 'لم يسجل دخول'; ?></td>
                            <td>
                                <?php if ($user['username'] != 'Admin' || $_SESSION['username'] == 'Admin'): ?>
                                    <button class="btn btn-sm btn-outline-primary" onclick="editUser(<?php echo $user['id']; ?>)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    
                                    <?php if ($user['id'] != $_SESSION['user_id'] && $user['username'] != 'Admin' && hasPermission('super_admin')): ?>
                                        <button class="btn btn-sm btn-outline-danger" onclick="deleteUser(<?php echo $user['id']; ?>)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Modal تعديل المستخدم -->
<div class="modal fade" id="editUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعديل المستخدم</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="editUserForm">
                <div class="modal-body">
                    <input type="hidden" name="user_id" id="edit_user_id">
                    
                    <div class="mb-3">
                        <label for="edit_full_name" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="edit_full_name" name="full_name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_email" class="form-label">البريد الإلكتروني</label>
                        <input type="email" class="form-control" id="edit_email" name="email">
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_phone" class="form-label">رقم الجوال</label>
                        <input type="tel" class="form-control" id="edit_phone" name="phone">
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_role" class="form-label">الصلاحية</label>
                        <select class="form-select" id="edit_role" name="role">
                            <option value="user">مستخدم</option>
                            <option value="manager">مدير</option>
                            <option value="super_admin">مدير عام</option>
                        </select>
                    </div>
                    
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="edit_is_active" name="is_active">
                        <label class="form-check-label" for="edit_is_active">
                            نشط
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" name="update_user" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
$extra_js = "
<script>
const users = " . json_encode($users) . ";

function editUser(userId) {
    const user = users.find(u => u.id == userId);
    if (!user) return;
    
    document.getElementById('edit_user_id').value = user.id;
    document.getElementById('edit_full_name').value = user.full_name;
    document.getElementById('edit_email').value = user.email || '';
    document.getElementById('edit_phone').value = user.phone || '';
    document.getElementById('edit_role').value = user.role;
    document.getElementById('edit_is_active').checked = user.is_active == 1;
    
    new bootstrap.Modal(document.getElementById('editUserModal')).show();
}

function deleteUser(userId) {
    if (confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = '<input type=\"hidden\" name=\"user_id\" value=\"' + userId + '\">' +
                        '<input type=\"hidden\" name=\"delete_user\" value=\"1\">';
        document.body.appendChild(form);
        form.submit();
    }
}

// البحث في الجدول
searchTable('searchInput', 'usersTable');
</script>
";

include 'includes/footer.php';
?>
