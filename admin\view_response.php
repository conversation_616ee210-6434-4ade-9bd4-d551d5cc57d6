<?php
/**
 * عرض تفاصيل الرد
 * View Response Details
 */

define('INCLUDED_FROM_APP', true);
require_once '../config.php';
require_once '../includes/security.php';

$page_title = 'تفاصيل الرد';

$response_id = intval($_GET['id'] ?? 0);

if (!$response_id) {
    redirect('reports.php');
}

// جلب بيانات الرد
$stmt = $pdo->prepare("
    SELECT 
        sr.*,
        c.name as company_name,
        b.name as branch_name,
        rep.name as rep_name
    FROM survey_responses sr
    LEFT JOIN companies c ON sr.company_code = c.code
    LEFT JOIN branches b ON sr.branch_code = b.code
    LEFT JOIN sales_representatives rep ON sr.sales_rep_code = rep.code
    WHERE sr.id = ?
");
$stmt->execute([$response_id]);
$response = $stmt->fetch();

if (!$response) {
    showMessage('الرد غير موجود', 'error');
    redirect('reports.php');
}

// جلب الإجابات
$stmt = $pdo->prepare("
    SELECT 
        sa.*,
        sq.question_text,
        sq.question_type,
        sq.question_order
    FROM survey_answers sa
    JOIN survey_questions sq ON sa.question_id = sq.id
    WHERE sa.response_id = ?
    ORDER BY sq.question_order
");
$stmt->execute([$response_id]);
$answers = $stmt->fetchAll();

include 'includes/header.php';
?>

<div class="page-header">
    <h1><i class="fas fa-eye me-3"></i><?php echo $page_title; ?></h1>
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="index.php">لوحة المعلومات</a></li>
            <li class="breadcrumb-item"><a href="reports.php">التقارير</a></li>
            <li class="breadcrumb-item active">تفاصيل الرد</li>
        </ol>
    </nav>
</div>

<div class="row">
    <!-- معلومات الرد -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-info-circle me-2"></i>معلومات الرد</h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <td><strong>رقم الفاتورة:</strong></td>
                        <td><?php echo htmlspecialchars($response['invoice_id']); ?></td>
                    </tr>
                    <tr>
                        <td><strong>الشركة:</strong></td>
                        <td><?php echo htmlspecialchars($response['company_name'] ?? 'غير محدد'); ?></td>
                    </tr>
                    <tr>
                        <td><strong>الفرع:</strong></td>
                        <td><?php echo htmlspecialchars($response['branch_name'] ?? 'غير محدد'); ?></td>
                    </tr>
                    <tr>
                        <td><strong>المندوب:</strong></td>
                        <td><?php echo htmlspecialchars($response['rep_name'] ?? 'غير محدد'); ?></td>
                    </tr>
                    <tr>
                        <td><strong>رقم العميل:</strong></td>
                        <td><?php echo htmlspecialchars($response['customer_phone']); ?></td>
                    </tr>
                    <tr>
                        <td><strong>تاريخ الإرسال:</strong></td>
                        <td><?php echo date('Y-m-d H:i:s', strtotime($response['submitted_at'])); ?></td>
                    </tr>
                    <tr>
                        <td><strong>عنوان IP:</strong></td>
                        <td><?php echo htmlspecialchars($response['ip_address'] ?? 'غير محدد'); ?></td>
                    </tr>
                </table>
            </div>
        </div>
        
        <!-- إحصائيات سريعة -->
        <div class="card mt-4">
            <div class="card-header">
                <h5><i class="fas fa-chart-pie me-2"></i>إحصائيات التقييم</h5>
            </div>
            <div class="card-body">
                <?php
                $rating_answers = array_filter($answers, function($answer) {
                    return $answer['rating_value'] !== null;
                });
                
                if (!empty($rating_answers)) {
                    $total_rating = array_sum(array_column($rating_answers, 'rating_value'));
                    $avg_rating = $total_rating / count($rating_answers);
                    
                    echo '<div class="text-center mb-3">';
                    echo '<h3 class="text-primary">' . round($avg_rating, 1) . '/5</h3>';
                    echo '<div class="rating-stars">';
                    for ($i = 1; $i <= 5; $i++) {
                        echo '<i class="fas fa-star ' . ($i <= $avg_rating ? 'text-warning' : 'text-muted') . '"></i>';
                    }
                    echo '</div>';
                    echo '<small class="text-muted">متوسط التقييم العام</small>';
                    echo '</div>';
                    
                    // توزيع التقييمات
                    $rating_distribution = array_count_values(array_column($rating_answers, 'rating_value'));
                    for ($i = 5; $i >= 1; $i--) {
                        $count = $rating_distribution[$i] ?? 0;
                        $percentage = count($rating_answers) > 0 ? ($count / count($rating_answers)) * 100 : 0;
                        
                        echo '<div class="d-flex align-items-center mb-2">';
                        echo '<span class="me-2">' . $i . ' نجوم</span>';
                        echo '<div class="progress flex-grow-1 me-2" style="height: 10px;">';
                        echo '<div class="progress-bar bg-warning" style="width: ' . $percentage . '%"></div>';
                        echo '</div>';
                        echo '<small class="text-muted">' . $count . '</small>';
                        echo '</div>';
                    }
                } else {
                    echo '<p class="text-muted text-center">لا توجد تقييمات</p>';
                }
                ?>
            </div>
        </div>
    </div>
    
    <!-- الإجابات -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-comments me-2"></i>الإجابات (<?php echo count($answers); ?>)</h5>
            </div>
            <div class="card-body">
                <?php if (empty($answers)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-comments fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد إجابات</h5>
                    </div>
                <?php else: ?>
                    <?php foreach ($answers as $index => $answer): ?>
                        <div class="answer-card mb-4 p-3 border rounded">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h6 class="mb-0">
                                    <span class="badge bg-primary me-2"><?php echo $index + 1; ?></span>
                                    <?php echo htmlspecialchars($answer['question_text']); ?>
                                </h6>
                                <span class="badge bg-<?php echo $answer['question_type'] == 'rating' ? 'warning' : 'info'; ?>">
                                    <?php echo $answer['question_type'] == 'rating' ? 'تقييم' : 'نص'; ?>
                                </span>
                            </div>
                            
                            <div class="answer-content">
                                <?php if ($answer['question_type'] == 'rating' && $answer['rating_value']): ?>
                                    <div class="d-flex align-items-center">
                                        <span class="h5 mb-0 me-3 text-primary"><?php echo $answer['rating_value']; ?>/5</span>
                                        <div class="rating-stars">
                                            <?php for ($i = 1; $i <= 5; $i++): ?>
                                                <i class="fas fa-star <?php echo $i <= $answer['rating_value'] ? 'text-warning' : 'text-muted'; ?>"></i>
                                            <?php endfor; ?>
                                        </div>
                                    </div>
                                <?php elseif ($answer['question_type'] == 'text' && $answer['answer_value']): ?>
                                    <div class="bg-light p-3 rounded">
                                        <p class="mb-0"><?php echo nl2br(htmlspecialchars($answer['answer_value'])); ?></p>
                                    </div>
                                <?php else: ?>
                                    <p class="text-muted mb-0"><em>لم يتم الإجابة على هذا السؤال</em></p>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<div class="text-center mt-4">
    <a href="reports.php" class="btn btn-secondary">
        <i class="fas fa-arrow-right me-2"></i>
        العودة إلى التقارير
    </a>
    
    <button onclick="window.print()" class="btn btn-primary">
        <i class="fas fa-print me-2"></i>
        طباعة
    </button>
</div>

<style>
.rating-stars {
    font-size: 1.2rem;
}

.rating-stars i {
    margin-left: 2px;
}

.answer-card {
    transition: all 0.3s ease;
}

.answer-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

@media print {
    .btn, .breadcrumb, .sidebar, .top-navbar {
        display: none !important;
    }
    
    .main-content {
        margin-right: 0 !important;
    }
    
    .card {
        border: 1px solid #ddd !important;
        box-shadow: none !important;
    }
}
</style>

<?php include 'includes/footer.php'; ?>
