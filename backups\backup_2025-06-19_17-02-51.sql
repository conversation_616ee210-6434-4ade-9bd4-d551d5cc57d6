-- نسخة احتياطية لقاعدة البيانات
-- تاريخ الإنشاء: 2025-06-19 17:02:51
-- المستخدم: مدير النظام

SET FOREIGN_KEY_CHECKS=0;

-- <PERSON><PERSON><PERSON><PERSON> الجدول `activity_logs`
DROP TABLE IF EXISTS `activity_logs`;
CREATE TABLE `activity_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `action` varchar(100) NOT NULL,
  `table_name` varchar(50) DEFAULT NULL,
  `record_id` int(11) DEFAULT NULL,
  `old_values` text DEFAULT NULL,
  `new_values` text DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  <PERSON>IMAR<PERSON>EY (`id`),
  <PERSON><PERSON>Y `user_id` (`user_id`),
  <PERSON><PERSON><PERSON> `action` (`action`),
  <PERSON><PERSON><PERSON> `created_at` (`created_at`),
  CONSTRAINT `activity_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- بيانات الجدول `activity_logs`
INSERT INTO `activity_logs` VALUES ('7', '1', 'login', 'users', '1', NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-19 16:26:54');
INSERT INTO `activity_logs` VALUES ('8', '3', 'login', 'users', '3', NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-19 16:30:29');
INSERT INTO `activity_logs` VALUES ('9', '3', 'update_user', 'users', '1', NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-19 16:39:53');
INSERT INTO `activity_logs` VALUES ('10', '3', 'update_user', 'users', '1', NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-19 16:41:05');

-- هيكل الجدول `blocked_ips`
DROP TABLE IF EXISTS `blocked_ips`;
CREATE TABLE `blocked_ips` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ip_address` varchar(45) NOT NULL,
  `reason` varchar(255) DEFAULT NULL,
  `blocked_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `expires_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `ip_address` (`ip_address`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- هيكل الجدول `branches`
DROP TABLE IF EXISTS `branches`;
CREATE TABLE `branches` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code` varchar(10) NOT NULL,
  `name` varchar(255) NOT NULL,
  `company_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`),
  KEY `company_id` (`company_id`),
  CONSTRAINT `branches_ibfk_1` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- بيانات الجدول `branches`
INSERT INTO `branches` VALUES ('1', '0109', 'المتجر انظمة', '1', '2025-06-19 16:41:28', '2025-06-19 16:41:28', '1', '0');
INSERT INTO `branches` VALUES ('2', '0101', 'فرع العليا انظمة', '1', '2025-06-19 16:41:28', '2025-06-19 16:41:28', '1', '0');
INSERT INTO `branches` VALUES ('3', '0105', 'مشاريع انظمة', '1', '2025-06-19 16:41:28', '2025-06-19 16:41:28', '1', '0');
INSERT INTO `branches` VALUES ('4', '0201', 'فرع العليا حلم', '2', '2025-06-19 16:41:28', '2025-06-19 16:41:28', '1', '0');
INSERT INTO `branches` VALUES ('5', '0209', 'المتجر حلم', '2', '2025-06-19 16:41:28', '2025-06-19 16:41:28', '1', '0');
INSERT INTO `branches` VALUES ('6', '0203', 'فرع صحاري مول، بوابه ٦', '2', '2025-06-19 16:41:28', '2025-06-19 16:41:28', '1', '0');
INSERT INTO `branches` VALUES ('7', '0205', 'مشاريع حلم', '2', '2025-06-19 16:41:28', '2025-06-19 16:41:28', '1', '0');

-- هيكل الجدول `companies`
DROP TABLE IF EXISTS `companies`;
CREATE TABLE `companies` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code` varchar(10) NOT NULL,
  `name` varchar(255) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- بيانات الجدول `companies`
INSERT INTO `companies` VALUES ('1', '01', 'شركة أنظمة جدة للتجارة', '2025-06-19 16:41:28', '2025-06-19 16:41:28', '1', '0');
INSERT INTO `companies` VALUES ('2', '02', 'شركة حلم المنزل للأثاث', '2025-06-19 16:41:28', '2025-06-19 16:41:28', '1', '0');

-- هيكل الجدول `login_attempts`
DROP TABLE IF EXISTS `login_attempts`;
CREATE TABLE `login_attempts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `attempts` int(11) NOT NULL DEFAULT 1,
  `last_attempt` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- هيكل الجدول `sales_representatives`
DROP TABLE IF EXISTS `sales_representatives`;
CREATE TABLE `sales_representatives` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code` varchar(10) NOT NULL,
  `name` varchar(255) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`)
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- بيانات الجدول `sales_representatives`
INSERT INTO `sales_representatives` VALUES ('1', 'ID0003', 'هاني سالم', '2025-06-19 16:41:28', '2025-06-19 16:41:28', '1', '0');
INSERT INTO `sales_representatives` VALUES ('2', 'ID0012', 'خالد سالم', '2025-06-19 16:41:28', '2025-06-19 16:41:28', '1', '0');
INSERT INTO `sales_representatives` VALUES ('3', 'ID0030', 'عبدالله صلاح', '2025-06-19 16:41:28', '2025-06-19 16:41:28', '1', '0');
INSERT INTO `sales_representatives` VALUES ('4', 'ID0141', 'مصطفى عبدالله', '2025-06-19 16:41:28', '2025-06-19 16:41:28', '1', '0');
INSERT INTO `sales_representatives` VALUES ('5', 'ID0041', 'عبد المجيد محمد', '2025-06-19 16:41:28', '2025-06-19 16:41:28', '1', '0');
INSERT INTO `sales_representatives` VALUES ('6', 'ID0025', 'عيسى سالم', '2025-06-19 16:41:28', '2025-06-19 16:41:28', '1', '0');
INSERT INTO `sales_representatives` VALUES ('7', 'ID0105', 'ياسر فرحان', '2025-06-19 16:41:28', '2025-06-19 16:41:28', '1', '0');
INSERT INTO `sales_representatives` VALUES ('8', 'ID0122', 'سعد محمد', '2025-06-19 16:41:28', '2025-06-19 16:41:28', '1', '0');
INSERT INTO `sales_representatives` VALUES ('9', 'ID0139', 'زيفه ناصر', '2025-06-19 16:41:28', '2025-06-19 16:41:28', '1', '0');
INSERT INTO `sales_representatives` VALUES ('10', 'ID0142', 'شهد محمد', '2025-06-19 16:41:28', '2025-06-19 16:41:28', '1', '0');
INSERT INTO `sales_representatives` VALUES ('11', 'ID0143', 'عبد الرحمن صالح', '2025-06-19 16:41:28', '2025-06-19 16:41:28', '1', '0');
INSERT INTO `sales_representatives` VALUES ('12', 'ID0145', 'شريفه حسين', '2025-06-19 16:41:28', '2025-06-19 16:41:28', '1', '0');
INSERT INTO `sales_representatives` VALUES ('13', 'ID0146', 'مها عمر', '2025-06-19 16:41:28', '2025-06-19 16:41:28', '1', '0');
INSERT INTO `sales_representatives` VALUES ('14', 'ID0147', 'دعاء حسان', '2025-06-19 16:41:28', '2025-06-19 16:41:28', '1', '0');
INSERT INTO `sales_representatives` VALUES ('15', 'ID0148', 'ناديه جمال', '2025-06-19 16:41:28', '2025-06-19 16:41:28', '1', '0');
INSERT INTO `sales_representatives` VALUES ('16', 'ID0011', 'مرعي عمر', '2025-06-19 16:41:28', '2025-06-19 16:41:28', '1', '0');
INSERT INTO `sales_representatives` VALUES ('17', 'ID0022', 'روان الدوسري', '2025-06-19 16:41:28', '2025-06-19 16:41:28', '1', '0');
INSERT INTO `sales_representatives` VALUES ('18', 'ID0028', 'محمد الشاطري', '2025-06-19 16:41:28', '2025-06-19 16:41:28', '1', '0');
INSERT INTO `sales_representatives` VALUES ('19', 'ID0014', 'عزيز محمود', '2025-06-19 16:41:28', '2025-06-19 16:41:28', '1', '0');

-- هيكل الجدول `survey_answers`
DROP TABLE IF EXISTS `survey_answers`;
CREATE TABLE `survey_answers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `response_id` int(11) NOT NULL,
  `question_id` int(11) NOT NULL,
  `answer_value` text DEFAULT NULL,
  `rating_value` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `response_id` (`response_id`),
  KEY `question_id` (`question_id`),
  CONSTRAINT `survey_answers_ibfk_1` FOREIGN KEY (`response_id`) REFERENCES `survey_responses` (`id`) ON DELETE CASCADE,
  CONSTRAINT `survey_answers_ibfk_2` FOREIGN KEY (`question_id`) REFERENCES `survey_questions` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- هيكل الجدول `survey_questions`
DROP TABLE IF EXISTS `survey_questions`;
CREATE TABLE `survey_questions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `question_text` text NOT NULL,
  `question_type` enum('rating','text') NOT NULL,
  `question_order` int(11) NOT NULL,
  `is_required` tinyint(1) NOT NULL DEFAULT 1,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- بيانات الجدول `survey_questions`
INSERT INTO `survey_questions` VALUES ('1', 'وش رأيك بمنتجاتنا؟ ✨', 'rating', '1', '1', '1', '2025-06-19 16:19:10', '2025-06-19 16:22:51');
INSERT INTO `survey_questions` VALUES ('2', 'جازت لك خدمة الموظف وما قصر معك؟ 👩‍💻👨🏻‍💻', 'rating', '2', '1', '1', '2025-06-19 16:19:30', '2025-06-19 16:22:51');
INSERT INTO `survey_questions` VALUES ('3', 'تنصح وتوصي الاهل والاصدقاء بـ حلم المنزل؟ 🗣', 'rating', '5', '1', '0', '2025-06-19 16:19:54', '2025-06-19 16:22:51');
INSERT INTO `survey_questions` VALUES ('4', 'تنصح وتوصي الاهل والاصدقاء بـ حلم المنزل؟ 🗣', 'rating', '3', '1', '1', '2025-06-19 16:19:54', '2025-06-19 16:22:51');
INSERT INTO `survey_questions` VALUES ('5', 'لو باقي شي بالخاطر أو ودك تقوله لنا، هذي مساحة فاضيه خذ فيها راحتك 🔴', 'text', '4', '0', '1', '2025-06-19 16:20:25', '2025-06-19 16:22:51');

-- هيكل الجدول `survey_responses`
DROP TABLE IF EXISTS `survey_responses`;
CREATE TABLE `survey_responses` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `invoice_id` varchar(50) NOT NULL,
  `company_code` varchar(10) NOT NULL,
  `branch_code` varchar(10) NOT NULL,
  `sales_rep_code` varchar(10) NOT NULL,
  `customer_phone` varchar(20) NOT NULL,
  `submitted_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `invoice_id` (`invoice_id`),
  KEY `company_code` (`company_code`),
  KEY `branch_code` (`branch_code`),
  KEY `sales_rep_code` (`sales_rep_code`),
  KEY `submitted_at` (`submitted_at`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- هيكل الجدول `survey_settings`
DROP TABLE IF EXISTS `survey_settings`;
CREATE TABLE `survey_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `survey_title` varchar(255) NOT NULL DEFAULT 'استبيان رضا العملاء',
  `survey_description` text DEFAULT NULL,
  `thank_you_message` text DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- بيانات الجدول `survey_settings`
INSERT INTO `survey_settings` VALUES ('1', 'استبيان رضا العملاء', 'شكراً لتسوقك معنا نحن متحمسون لسماع رأيك في تجربتك معنا هنا:', 'شكراً لك على وقتك في الإجابة على الاستبيان. رأيك مهم جداً لنا ويساعدنا على تحسين خدماتنا.', '1', '2025-06-19 16:18:10', '2025-06-19 16:18:10');

-- هيكل الجدول `user_sessions`
DROP TABLE IF EXISTS `user_sessions`;
CREATE TABLE `user_sessions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `session_id` varchar(128) NOT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `last_activity` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `session_id` (`session_id`),
  KEY `user_id` (`user_id`),
  KEY `last_activity` (`last_activity`),
  CONSTRAINT `user_sessions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- بيانات الجدول `user_sessions`
INSERT INTO `user_sessions` VALUES ('1', '1', 'bc6581f21debd8127eac97f8681950c8', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-19 16:26:54', '2025-06-19 16:26:54');
INSERT INTO `user_sessions` VALUES ('2', '3', 'e98be1a964cc06735065abe47ec1626d', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-19 16:30:29', '2025-06-19 16:30:29');

-- هيكل الجدول `users`
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `full_name` varchar(255) NOT NULL,
  `email` varchar(255) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `role` enum('super_admin','manager','user') NOT NULL DEFAULT 'user',
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `last_login` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- بيانات الجدول `users`
INSERT INTO `users` VALUES ('1', 'eso', '$2y$10$qdUXG77VqDLUXvxA8e0m3.XAii6APjuSOFjMsa0r7wLwKmpGMint.', 'Esmat Omer', '<EMAIL>', '0533362837', 'manager', '1', '0', '2025-06-19 16:24:08', '2025-06-19 16:41:05', '2025-06-19 16:26:54');
INSERT INTO `users` VALUES ('3', 'Admin', '$2y$10$RFnjEWC..RPmLZ03.YMP7elTTlt2t4ApwZ5E2bkZS6iTSnVX6n9IG', 'مدير النظام', '<EMAIL>', NULL, 'super_admin', '1', '0', '2025-06-19 16:30:17', '2025-06-19 16:30:29', '2025-06-19 16:30:29');

SET FOREIGN_KEY_CHECKS=1;
