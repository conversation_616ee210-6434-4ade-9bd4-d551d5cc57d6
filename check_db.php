<?php
/**
 * فحص قاعدة البيانات
 * Database Check
 */

// إعدادات قاعدة البيانات
$servername = "localhost";
$username_db = "root";
$password_db = "";
$dbname = "jst_survey";

try {
    // محاولة الاتصال بقاعدة البيانات
    $pdo = new PDO("mysql:host=$servername;dbname=$dbname;charset=utf8mb4", $username_db, $password_db);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>✅ تم الاتصال بقاعدة البيانات بنجاح</h2>";
    
    // فحص الجداول
    $tables = [
        'users',
        'companies', 
        'branches',
        'sales_representatives',
        'survey_questions',
        'survey_settings',
        'survey_responses',
        'survey_answers',
        'activity_logs',
        'user_sessions',
        'login_attempts',
        'blocked_ips'
    ];
    
    echo "<h3>فحص الجداول:</h3>";
    echo "<ul>";
    
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM `$table`");
            $count = $stmt->fetchColumn();
            echo "<li style='color: green;'>✓ جدول $table موجود ($count سجل)</li>";
        } catch (Exception $e) {
            echo "<li style='color: red;'>✗ جدول $table غير موجود</li>";
        }
    }
    
    echo "</ul>";
    
    // فحص وجود مستخدمين في النظام
    try {
        $stmt = $pdo->prepare("SELECT COUNT(*) as user_count FROM users WHERE is_active = 1");
        $stmt->execute();
        $result = $stmt->fetch();

        if ($result['user_count'] > 0) {
            echo "<h3>✅ يوجد " . $result['user_count'] . " مستخدم نشط في النظام</h3>";
        } else {
            echo "<h3 style='color: red;'>❌ لا يوجد مستخدمين نشطين في النظام</h3>";
        }
    } catch (Exception $e) {
        echo "<h3 style='color: red;'>❌ خطأ في فحص المستخدمين: " . $e->getMessage() . "</h3>";
    }
    
    // فحص الأسئلة الافتراضية
    try {
        $stmt = $pdo->query("SELECT COUNT(*) FROM survey_questions");
        $questions_count = $stmt->fetchColumn();
        
        if ($questions_count > 0) {
            echo "<h3>✅ الأسئلة الافتراضية موجودة ($questions_count سؤال)</h3>";
            
            $stmt = $pdo->query("SELECT question_text, question_type FROM survey_questions ORDER BY question_order");
            $questions = $stmt->fetchAll();
            
            echo "<ol>";
            foreach ($questions as $q) {
                echo "<li>" . htmlspecialchars($q['question_text']) . " (" . $q['question_type'] . ")</li>";
            }
            echo "</ol>";
        } else {
            echo "<h3 style='color: red;'>❌ لا توجد أسئلة افتراضية</h3>";
        }
    } catch (Exception $e) {
        echo "<h3 style='color: red;'>❌ خطأ في فحص الأسئلة: " . $e->getMessage() . "</h3>";
    }
    
    // فحص الشركات والفروع
    try {
        $stmt = $pdo->query("SELECT COUNT(*) FROM companies");
        $companies_count = $stmt->fetchColumn();
        
        $stmt = $pdo->query("SELECT COUNT(*) FROM branches");
        $branches_count = $stmt->fetchColumn();
        
        $stmt = $pdo->query("SELECT COUNT(*) FROM sales_representatives");
        $reps_count = $stmt->fetchColumn();
        
        echo "<h3>البيانات الافتراضية:</h3>";
        echo "<ul>";
        echo "<li>الشركات: $companies_count</li>";
        echo "<li>الفروع: $branches_count</li>";
        echo "<li>المندوبين: $reps_count</li>";
        echo "</ul>";
        
    } catch (Exception $e) {
        echo "<h3 style='color: red;'>❌ خطأ في فحص البيانات: " . $e->getMessage() . "</h3>";
    }
    
    echo "<hr>";
    echo "<h3>🎉 النظام جاهز للاستخدام!</h3>";
    echo "<p><a href='admin/login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>الذهاب إلى لوحة الإدارة</a></p>";
    echo "<p><a href='index.php?inv=TEST123&le=01&br=0101&sm=ID0003&cp=0501234567' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>تجربة الاستبيان</a></p>";
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>❌ خطأ في الاتصال بقاعدة البيانات</h2>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "<p>يرجى تشغيل معالج التثبيت أولاً: <a href='install.php?install=1'>تثبيت قاعدة البيانات</a></p>";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فحص قاعدة البيانات - نظام الاستبيان</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        
        h1, h2, h3 {
            color: #333;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        ul, ol {
            line-height: 1.6;
        }
        
        a {
            margin: 5px;
            display: inline-block;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 فحص قاعدة البيانات - نظام استبيان JST</h1>
    </div>
</body>
</html>
