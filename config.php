<?php
/**
 * ملف إعدادات قاعدة البيانات
 * JST Survey System Configuration File
 */

// إعدادات قاعدة البيانات
$servername = "localhost";
$username_db = "root";
$password_db = "";
$dbname = "jst_survey";

// إنشاء الاتصال بقاعدة البيانات
try {
    $pdo = new PDO("mysql:host=$servername;dbname=$dbname;charset=utf8mb4", $username_db, $password_db);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
    $pdo->setAttribute(PDO::ATTR_EMULATE_PREPARES, false);
} catch(PDOException $e) {
    die("فشل الاتصال بقاعدة البيانات: " . $e->getMessage());
}

// إعدادات عامة للنظام
define('SITE_URL', 'http://localhost/dhsurvey/');
define('ADMIN_URL', SITE_URL . 'admin/');
define('ASSETS_URL', SITE_URL . 'assets/');
define('UPLOADS_DIR', __DIR__ . '/uploads/');
define('BACKUPS_DIR', __DIR__ . '/backups/');
define('LOGS_DIR', __DIR__ . '/logs/');

// إعدادات الأمان
define('SESSION_TIMEOUT', 3600); // مهلة الجلسة بالثواني (ساعة واحدة)
define('MAX_LOGIN_ATTEMPTS', 5); // عدد محاولات تسجيل الدخول المسموحة
define('LOGIN_BLOCK_TIME', 900); // مدة الحظر بالثواني (15 دقيقة)
define('PASSWORD_MIN_LENGTH', 8); // الحد الأدنى لطول كلمة المرور

// إعدادات التشفير
define('ENCRYPTION_KEY', 'jst_survey_2025_secure_key_#@!$%^&*');
define('HASH_ALGO', 'sha256');

// إعدادات الملفات
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5 ميجابايت
define('ALLOWED_FILE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx', 'xls', 'xlsx']);

// إعدادات البريد الإلكتروني (اختيارية)
define('SMTP_HOST', '');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '');
define('SMTP_PASSWORD', '');
define('FROM_EMAIL', '<EMAIL>');
define('FROM_NAME', 'JST Survey System');

// إعدادات التطبيق
define('APP_NAME', 'نظام استبيان JST');
define('APP_VERSION', '1.0.0');
define('TIMEZONE', 'Asia/Riyadh');

// تعيين المنطقة الزمنية
date_default_timezone_set(TIMEZONE);

// بدء الجلسة إذا لم تكن مبدوءة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// دالة لتسجيل الأخطاء
function logError($message, $file = '', $line = '') {
    $logFile = LOGS_DIR . 'error_' . date('Y-m-d') . '.log';
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] ERROR: $message";
    if ($file) $logMessage .= " in $file";
    if ($line) $logMessage .= " on line $line";
    $logMessage .= PHP_EOL;
    
    if (!file_exists(LOGS_DIR)) {
        mkdir(LOGS_DIR, 0755, true);
    }
    
    file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
}

// دالة لتسجيل العمليات
function logActivity($userId, $action, $tableName = null, $recordId = null, $oldValues = null, $newValues = null) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            INSERT INTO activity_logs (user_id, action, table_name, record_id, old_values, new_values, ip_address, user_agent) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            $userId,
            $action,
            $tableName,
            $recordId,
            $oldValues ? json_encode($oldValues) : null,
            $newValues ? json_encode($newValues) : null,
            $_SERVER['REMOTE_ADDR'] ?? null,
            $_SERVER['HTTP_USER_AGENT'] ?? null
        ]);
    } catch (Exception $e) {
        logError("Failed to log activity: " . $e->getMessage());
    }
}

// دالة للتحقق من صحة البيانات
function sanitizeInput($data) {
    if (is_array($data)) {
        return array_map('sanitizeInput', $data);
    }
    return htmlspecialchars(strip_tags(trim($data)), ENT_QUOTES, 'UTF-8');
}

// دالة للتحقق من صحة البريد الإلكتروني
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

// دالة للتحقق من قوة كلمة المرور
function validatePassword($password) {
    return strlen($password) >= PASSWORD_MIN_LENGTH && 
           preg_match('/[A-Z]/', $password) && 
           preg_match('/[a-z]/', $password) && 
           preg_match('/[0-9]/', $password);
}

// دالة لتشفير كلمة المرور
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

// دالة للتحقق من كلمة المرور
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

// دالة لإنشاء رمز عشوائي
function generateRandomToken($length = 32) {
    return bin2hex(random_bytes($length / 2));
}

// دالة للتحقق من الصلاحيات
function hasPermission($requiredRole) {
    if (!isset($_SESSION['user_role'])) {
        return false;
    }
    
    $roles = ['user' => 1, 'manager' => 2, 'super_admin' => 3];
    $userLevel = $roles[$_SESSION['user_role']] ?? 0;
    $requiredLevel = $roles[$requiredRole] ?? 0;
    
    return $userLevel >= $requiredLevel;
}

// دالة للتحقق من تسجيل الدخول
function isLoggedIn() {
    return isset($_SESSION['user_id']) && isset($_SESSION['user_role']);
}

// دالة لإعادة التوجيه
function redirect($url) {
    header("Location: $url");
    exit();
}

// دالة لعرض الرسائل
function showMessage($message, $type = 'info') {
    $_SESSION['message'] = $message;
    $_SESSION['message_type'] = $type;
}

// دالة لاسترجاع وحذف الرسائل
function getMessage() {
    if (isset($_SESSION['message'])) {
        $message = $_SESSION['message'];
        $type = $_SESSION['message_type'] ?? 'info';
        unset($_SESSION['message'], $_SESSION['message_type']);
        return ['message' => $message, 'type' => $type];
    }
    return null;
}

// إنشاء المجلدات المطلوبة إذا لم تكن موجودة
$directories = [UPLOADS_DIR, BACKUPS_DIR, LOGS_DIR];
foreach ($directories as $dir) {
    if (!file_exists($dir)) {
        mkdir($dir, 0755, true);
    }
}

?>
