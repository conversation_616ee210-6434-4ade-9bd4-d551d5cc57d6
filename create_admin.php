<?php
/**
 * إنشاء مستخدم مدير جديد
 * Create New Admin User
 */

require_once 'config.php';

$success_message = '';
$error_message = '';

// معالجة إنشاء المستخدم
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $username = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    $full_name = trim($_POST['full_name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    
    // التحقق من البيانات
    if (empty($username) || empty($password) || empty($full_name)) {
        $error_message = 'جميع الحقول المطلوبة يجب ملؤها';
    } elseif (strlen($password) < 8) {
        $error_message = 'كلمة المرور يجب أن تكون 8 أحرف على الأقل';
    } elseif ($password !== $confirm_password) {
        $error_message = 'كلمة المرور وتأكيد كلمة المرور غير متطابقتين';
    } else {
        try {
            // التحقق من عدم وجود مستخدم بنفس الاسم
            $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ?");
            $stmt->execute([$username]);
            if ($stmt->fetch()) {
                $error_message = 'اسم المستخدم موجود مسبقاً';
            } else {
                // إنشاء المستخدم الجديد
                $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                
                $stmt = $pdo->prepare("
                    INSERT INTO users (username, password, full_name, email, role, is_active) 
                    VALUES (?, ?, ?, ?, 'super_admin', 1)
                ");
                $stmt->execute([$username, $hashed_password, $full_name, $email]);
                
                $success_message = 'تم إنشاء المستخدم بنجاح! يمكنك الآن تسجيل الدخول.';
            }
        } catch (Exception $e) {
            $error_message = 'حدث خطأ: ' . $e->getMessage();
        }
    }
}

// التحقق من وجود مستخدمين
$stmt = $pdo->query("SELECT COUNT(*) as count FROM users WHERE is_active = 1");
$user_count = $stmt->fetchColumn();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء مستخدم مدير - نظام الاستبيان</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .create-admin-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 500px;
            width: 100%;
            margin: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .form-container {
            padding: 30px;
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-create {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            color: white;
            font-weight: 600;
            width: 100%;
            transition: all 0.3s;
        }
        
        .btn-create:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            color: white;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
        
        .password-strength {
            font-size: 0.875rem;
            margin-top: 5px;
        }
        
        .strength-weak { color: #dc3545; }
        .strength-medium { color: #ffc107; }
        .strength-strong { color: #28a745; }
    </style>
</head>
<body>
    <div class="create-admin-container">
        <div class="header">
            <i class="fas fa-user-shield fa-3x mb-3"></i>
            <h2>إنشاء مستخدم مدير</h2>
            <p class="mb-0">نظام استبيان JST</p>
        </div>
        
        <div class="form-container">
            <?php if ($user_count > 0): ?>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تنبيه:</strong> يوجد بالفعل <?php echo $user_count; ?> مستخدم في النظام.
                    <br>
                    <a href="admin/login.php" class="btn btn-sm btn-outline-primary mt-2">
                        <i class="fas fa-sign-in-alt me-1"></i>
                        تسجيل الدخول
                    </a>
                </div>
            <?php endif; ?>
            
            <?php if ($success_message): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo htmlspecialchars($success_message); ?>
                    <br>
                    <a href="admin/login.php" class="btn btn-sm btn-success mt-2">
                        <i class="fas fa-sign-in-alt me-1"></i>
                        تسجيل الدخول الآن
                    </a>
                </div>
            <?php endif; ?>
            
            <?php if ($error_message): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            <?php endif; ?>
            
            <form method="POST" id="createAdminForm">
                <div class="mb-3">
                    <label for="username" class="form-label">
                        <i class="fas fa-user me-2"></i>
                        اسم المستخدم <span class="text-danger">*</span>
                    </label>
                    <input type="text" class="form-control" id="username" name="username" 
                           value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>" 
                           required minlength="3" maxlength="50">
                    <div class="form-text">3-50 حرف، أحرف وأرقام فقط</div>
                </div>
                
                <div class="mb-3">
                    <label for="full_name" class="form-label">
                        <i class="fas fa-id-card me-2"></i>
                        الاسم الكامل <span class="text-danger">*</span>
                    </label>
                    <input type="text" class="form-control" id="full_name" name="full_name" 
                           value="<?php echo htmlspecialchars($_POST['full_name'] ?? ''); ?>" 
                           required maxlength="255">
                </div>
                
                <div class="mb-3">
                    <label for="email" class="form-label">
                        <i class="fas fa-envelope me-2"></i>
                        البريد الإلكتروني
                    </label>
                    <input type="email" class="form-control" id="email" name="email" 
                           value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" 
                           maxlength="255">
                </div>
                
                <div class="mb-3">
                    <label for="password" class="form-label">
                        <i class="fas fa-lock me-2"></i>
                        كلمة المرور <span class="text-danger">*</span>
                    </label>
                    <input type="password" class="form-control" id="password" name="password" 
                           required minlength="8">
                    <div id="passwordStrength" class="password-strength"></div>
                    <div class="form-text">8 أحرف على الأقل</div>
                </div>
                
                <div class="mb-4">
                    <label for="confirm_password" class="form-label">
                        <i class="fas fa-lock me-2"></i>
                        تأكيد كلمة المرور <span class="text-danger">*</span>
                    </label>
                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                           required minlength="8">
                    <div id="passwordMatch" class="form-text"></div>
                </div>
                
                <button type="submit" class="btn btn-create">
                    <i class="fas fa-user-plus me-2"></i>
                    إنشاء المستخدم
                </button>
            </form>
            
            <div class="text-center mt-3">
                <small class="text-muted">
                    <i class="fas fa-shield-alt me-1"></i>
                    سيتم إنشاء المستخدم بصلاحيات مدير عام
                </small>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // فحص قوة كلمة المرور
        document.getElementById('password').addEventListener('input', function() {
            const password = this.value;
            const strengthDiv = document.getElementById('passwordStrength');
            
            if (password.length === 0) {
                strengthDiv.textContent = '';
                return;
            }
            
            let strength = 0;
            if (password.length >= 8) strength++;
            if (/[a-z]/.test(password)) strength++;
            if (/[A-Z]/.test(password)) strength++;
            if (/[0-9]/.test(password)) strength++;
            if (/[^A-Za-z0-9]/.test(password)) strength++;
            
            if (strength < 2) {
                strengthDiv.textContent = 'ضعيفة';
                strengthDiv.className = 'password-strength strength-weak';
            } else if (strength < 4) {
                strengthDiv.textContent = 'متوسطة';
                strengthDiv.className = 'password-strength strength-medium';
            } else {
                strengthDiv.textContent = 'قوية';
                strengthDiv.className = 'password-strength strength-strong';
            }
        });
        
        // فحص تطابق كلمة المرور
        function checkPasswordMatch() {
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirm_password').value;
            const matchDiv = document.getElementById('passwordMatch');
            
            if (confirmPassword.length === 0) {
                matchDiv.textContent = '';
                return;
            }
            
            if (password === confirmPassword) {
                matchDiv.textContent = '✓ كلمة المرور متطابقة';
                matchDiv.style.color = '#28a745';
            } else {
                matchDiv.textContent = '✗ كلمة المرور غير متطابقة';
                matchDiv.style.color = '#dc3545';
            }
        }
        
        document.getElementById('password').addEventListener('input', checkPasswordMatch);
        document.getElementById('confirm_password').addEventListener('input', checkPasswordMatch);
        
        // التحقق من النموذج قبل الإرسال
        document.getElementById('createAdminForm').addEventListener('submit', function(e) {
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirm_password').value;
            
            if (password !== confirmPassword) {
                e.preventDefault();
                alert('كلمة المرور وتأكيد كلمة المرور غير متطابقتين');
                return;
            }
            
            if (password.length < 8) {
                e.preventDefault();
                alert('كلمة المرور يجب أن تكون 8 أحرف على الأقل');
                return;
            }
        });
    </script>
</body>
</html>
