<?php
/**
 * إصلاح قاعدة البيانات
 * Database Fix Script
 */

// إعدادات قاعدة البيانات
$servername = "localhost";
$username_db = "root";
$password_db = "";
$dbname = "jst_survey";

try {
    // الاتصال بـ MySQL
    $pdo = new PDO("mysql:host=$servername;charset=utf8mb4", $username_db, $password_db);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>🔧 إصلاح قاعدة البيانات</h2>";
    
    // حذف قاعدة البيانات القديمة وإعادة إنشائها
    $pdo->exec("DROP DATABASE IF EXISTS `$dbname`");
    echo "<p style='color: orange;'>تم حذف قاعدة البيانات القديمة</p>";
    
    $pdo->exec("CREATE DATABASE `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "<p style='color: green;'>تم إنشاء قاعدة البيانات الجديدة</p>";
    
    $pdo->exec("USE `$dbname`");
    
    // قراءة ملف SQL
    if (!file_exists('database.sql')) {
        throw new Exception("ملف database.sql غير موجود");
    }
    
    $sql = file_get_contents('database.sql');
    if ($sql === false) {
        throw new Exception("لا يمكن قراءة ملف database.sql");
    }
    
    // تنفيذ الاستعلامات
    $statements = explode(';', $sql);
    $successCount = 0;
    $errorCount = 0;
    
    echo "<h3>تنفيذ الاستعلامات:</h3>";
    
    foreach ($statements as $statement) {
        $statement = trim($statement);
        
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue;
        }
        
        try {
            $pdo->exec($statement);
            $successCount++;
            echo "<p style='color: green;'>✓ تم تنفيذ استعلام بنجاح</p>";
        } catch (PDOException $e) {
            $errorCount++;
            echo "<p style='color: red;'>✗ خطأ: " . $e->getMessage() . "</p>";
            echo "<pre style='background: #f5f5f5; padding: 10px; font-size: 12px;'>" . htmlspecialchars(substr($statement, 0, 200)) . "...</pre>";
        }
    }
    
    echo "<hr>";
    echo "<h3>النتائج:</h3>";
    echo "<p><strong>الاستعلامات الناجحة:</strong> $successCount</p>";
    echo "<p><strong>الاستعلامات الفاشلة:</strong> $errorCount</p>";
    
    if ($errorCount == 0) {
        echo "<div style='background: #d4edda; color: #155724; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4>🎉 تم إصلاح قاعدة البيانات بنجاح!</h4>";
        echo "<p>جميع الجداول تم إنشاؤها بنجاح.</p>";
        echo "</div>";
        
        // التحقق من الجداول
        echo "<h3>التحقق من الجداول:</h3>";
        $tables = [
            'users', 'companies', 'branches', 'sales_representatives',
            'survey_questions', 'survey_settings', 'survey_responses', 
            'survey_answers', 'activity_logs', 'user_sessions', 
            'login_attempts', 'blocked_ips'
        ];
        
        echo "<ul>";
        foreach ($tables as $table) {
            try {
                $stmt = $pdo->query("SELECT COUNT(*) FROM `$table`");
                $count = $stmt->fetchColumn();
                echo "<li style='color: green;'>✓ جدول $table: $count سجل</li>";
            } catch (Exception $e) {
                echo "<li style='color: red;'>✗ جدول $table: غير موجود</li>";
            }
        }
        echo "</ul>";
        
        echo "<div style='margin: 20px 0;'>";
        echo "<a href='admin/login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>لوحة الإدارة</a>";
        echo "<a href='index.php?inv=TEST123&le=01&br=0101&sm=ID0003&cp=0501234567' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>تجربة الاستبيان</a>";
        echo "<a href='test_system.php' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>اختبار النظام</a>";
        echo "</div>";
        
    } else {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4>⚠️ حدثت أخطاء أثناء الإصلاح</h4>";
        echo "<p>يرجى مراجعة الأخطاء أعلاه.</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>❌ خطأ في الاتصال</h4>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح قاعدة البيانات - نظام استبيان JST</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        
        h1, h2, h3 {
            color: #333;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        pre {
            font-size: 12px;
            overflow-x: auto;
            max-height: 200px;
        }
        
        ul {
            line-height: 1.6;
        }
        
        a {
            margin: 5px;
            display: inline-block;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 إصلاح قاعدة البيانات - نظام استبيان JST</h1>
        <p>هذا المعالج سيقوم بحذف قاعدة البيانات القديمة وإعادة إنشائها من جديد.</p>
    </div>
</body>
</html>
