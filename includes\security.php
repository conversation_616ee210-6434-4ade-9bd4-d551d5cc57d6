<?php
/**
 * ملف الأمان والحماية
 * Security and Protection Functions
 */

// منع الوصول المباشر للملف
if (!defined('INCLUDED_FROM_APP')) {
    die('Access denied');
}

/**
 * فئة الأمان الرئيسية
 */
class Security {
    
    /**
     * التحقق من محاولات تسجيل الدخول
     */
    public static function checkLoginAttempts($username, $pdo) {
        $stmt = $pdo->prepare("SELECT attempts, last_attempt FROM login_attempts WHERE username = ?");
        $stmt->execute([$username]);
        $result = $stmt->fetch();
        
        if ($result) {
            $timeDiff = time() - strtotime($result['last_attempt']);
            
            // إذا تجاوز عدد المحاولات المسموح
            if ($result['attempts'] >= MAX_LOGIN_ATTEMPTS) {
                // إذا لم تنته مدة الحظر بعد
                if ($timeDiff < LOGIN_BLOCK_TIME) {
                    $remainingTime = LOGIN_BLOCK_TIME - $timeDiff;
                    throw new Exception("تم حظر حسابك مؤقتاً. حاول مرة أخرى بعد " . ceil($remainingTime / 60) . " دقيقة");
                } else {
                    // إعادة تعيين المحاولات بعد انتهاء مدة الحظر
                    self::resetLoginAttempts($username, $pdo);
                }
            }
        }
        
        return true;
    }
    
    /**
     * تسجيل محاولة دخول فاشلة
     */
    public static function recordFailedLogin($username, $pdo) {
        $stmt = $pdo->prepare("
            INSERT INTO login_attempts (username, attempts, last_attempt) 
            VALUES (?, 1, NOW()) 
            ON DUPLICATE KEY UPDATE 
            attempts = attempts + 1, 
            last_attempt = NOW()
        ");
        $stmt->execute([$username]);
    }
    
    /**
     * إعادة تعيين محاولات تسجيل الدخول
     */
    public static function resetLoginAttempts($username, $pdo) {
        $stmt = $pdo->prepare("DELETE FROM login_attempts WHERE username = ?");
        $stmt->execute([$username]);
    }
    
    /**
     * التحقق من عنوان IP المحظور
     */
    public static function checkBlockedIP($pdo) {
        $ip = $_SERVER['REMOTE_ADDR'];
        
        $stmt = $pdo->prepare("
            SELECT id FROM blocked_ips 
            WHERE ip_address = ? 
            AND (expires_at IS NULL OR expires_at > NOW())
        ");
        $stmt->execute([$ip]);
        
        if ($stmt->fetch()) {
            http_response_code(403);
            die('تم حظر عنوان IP الخاص بك');
        }
    }
    
    /**
     * حظر عنوان IP
     */
    public static function blockIP($ip, $reason, $duration = null, $pdo) {
        $expiresAt = $duration ? date('Y-m-d H:i:s', time() + $duration) : null;
        
        $stmt = $pdo->prepare("
            INSERT INTO blocked_ips (ip_address, reason, expires_at) 
            VALUES (?, ?, ?) 
            ON DUPLICATE KEY UPDATE 
            reason = VALUES(reason), 
            expires_at = VALUES(expires_at), 
            blocked_at = NOW()
        ");
        $stmt->execute([$ip, $reason, $expiresAt]);
    }
    
    /**
     * التحقق من صحة الجلسة
     */
    public static function validateSession($pdo) {
        if (!isset($_SESSION['user_id']) || !isset($_SESSION['session_token'])) {
            return false;
        }
        
        $stmt = $pdo->prepare("
            SELECT u.id, u.username, u.role, u.is_active, s.last_activity 
            FROM users u 
            JOIN user_sessions s ON u.id = s.user_id 
            WHERE u.id = ? AND s.session_id = ? AND u.is_active = 1 AND u.is_deleted = 0
        ");
        $stmt->execute([$_SESSION['user_id'], $_SESSION['session_token']]);
        $user = $stmt->fetch();
        
        if (!$user) {
            self::destroySession();
            return false;
        }
        
        // التحقق من انتهاء صلاحية الجلسة
        $lastActivity = strtotime($user['last_activity']);
        if (time() - $lastActivity > SESSION_TIMEOUT) {
            self::destroySession();
            return false;
        }
        
        // تحديث وقت آخر نشاط
        self::updateSessionActivity($_SESSION['user_id'], $_SESSION['session_token'], $pdo);
        
        return $user;
    }
    
    /**
     * إنشاء جلسة جديدة
     */
    public static function createSession($userId, $pdo) {
        $sessionId = generateRandomToken();
        
        // حذف الجلسات القديمة للمستخدم
        $stmt = $pdo->prepare("DELETE FROM user_sessions WHERE user_id = ?");
        $stmt->execute([$userId]);
        
        // إنشاء جلسة جديدة
        $stmt = $pdo->prepare("
            INSERT INTO user_sessions (user_id, session_id, ip_address, user_agent) 
            VALUES (?, ?, ?, ?)
        ");
        $stmt->execute([
            $userId,
            $sessionId,
            $_SERVER['REMOTE_ADDR'] ?? null,
            $_SERVER['HTTP_USER_AGENT'] ?? null
        ]);
        
        $_SESSION['session_token'] = $sessionId;
        
        return $sessionId;
    }
    
    /**
     * تحديث وقت آخر نشاط للجلسة
     */
    public static function updateSessionActivity($userId, $sessionId, $pdo) {
        $stmt = $pdo->prepare("
            UPDATE user_sessions 
            SET last_activity = NOW() 
            WHERE user_id = ? AND session_id = ?
        ");
        $stmt->execute([$userId, $sessionId]);
    }
    
    /**
     * تدمير الجلسة
     */
    public static function destroySession() {
        if (isset($_SESSION['user_id']) && isset($_SESSION['session_token'])) {
            global $pdo;
            $stmt = $pdo->prepare("DELETE FROM user_sessions WHERE user_id = ? AND session_id = ?");
            $stmt->execute([$_SESSION['user_id'], $_SESSION['session_token']]);
        }
        
        session_destroy();
        session_start();
    }
    
    /**
     * التحقق من رمز CSRF
     */
    public static function validateCSRF($token) {
        return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
    }
    
    /**
     * إنشاء رمز CSRF
     */
    public static function generateCSRF() {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = generateRandomToken();
        }
        return $_SESSION['csrf_token'];
    }
    
    /**
     * تنظيف البيانات من الهجمات
     */
    public static function sanitizeInput($data) {
        if (is_array($data)) {
            return array_map([self::class, 'sanitizeInput'], $data);
        }
        
        // إزالة المسافات الزائدة
        $data = trim($data);
        
        // تحويل الأحرف الخاصة
        $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
        
        // إزالة العلامات
        $data = strip_tags($data);
        
        return $data;
    }
    
    /**
     * التحقق من صحة البريد الإلكتروني
     */
    public static function validateEmail($email) {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }
    
    /**
     * التحقق من قوة كلمة المرور
     */
    public static function validatePassword($password) {
        // على الأقل 8 أحرف، حرف كبير، حرف صغير، رقم
        return strlen($password) >= PASSWORD_MIN_LENGTH && 
               preg_match('/[A-Z]/', $password) && 
               preg_match('/[a-z]/', $password) && 
               preg_match('/[0-9]/', $password);
    }
    
    /**
     * تشفير البيانات الحساسة
     */
    public static function encrypt($data) {
        $key = ENCRYPTION_KEY;
        $iv = openssl_random_pseudo_bytes(16);
        $encrypted = openssl_encrypt($data, 'AES-256-CBC', $key, 0, $iv);
        return base64_encode($iv . $encrypted);
    }
    
    /**
     * فك تشفير البيانات
     */
    public static function decrypt($data) {
        $key = ENCRYPTION_KEY;
        $data = base64_decode($data);
        $iv = substr($data, 0, 16);
        $encrypted = substr($data, 16);
        return openssl_decrypt($encrypted, 'AES-256-CBC', $key, 0, $iv);
    }
    
    /**
     * تنظيف الجلسات المنتهية الصلاحية
     */
    public static function cleanExpiredSessions($pdo) {
        $stmt = $pdo->prepare("
            DELETE FROM user_sessions 
            WHERE last_activity < DATE_SUB(NOW(), INTERVAL ? SECOND)
        ");
        $stmt->execute([SESSION_TIMEOUT]);
    }
    
    /**
     * تنظيف محاولات تسجيل الدخول القديمة
     */
    public static function cleanOldLoginAttempts($pdo) {
        $stmt = $pdo->prepare("
            DELETE FROM login_attempts 
            WHERE last_attempt < DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ");
        $stmt->execute();
    }
}

// تنظيف دوري للبيانات القديمة (يتم تشغيله عشوائياً)
if (rand(1, 100) == 1) {
    Security::cleanExpiredSessions($pdo);
    Security::cleanOldLoginAttempts($pdo);
}

?>
