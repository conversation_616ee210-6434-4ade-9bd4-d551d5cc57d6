<?php
/**
 * الصفحة الرئيسية للاستبيان
 * Main Survey Page
 */

define('INCLUDED_FROM_APP', true);
require_once 'config.php';
require_once 'includes/security.php';

// التحقق من عنوان IP المحظور
Security::checkBlockedIP($pdo);

// استلام المعاملات من الرابط
$invoice_id = $_GET['inv'] ?? '';
$company_code = $_GET['le'] ?? '';
$branch_code = $_GET['br'] ?? '';
$sales_rep_code = $_GET['sm'] ?? '';
$customer_phone = $_GET['cp'] ?? '';

// التحقق من وجود المعاملات المطلوبة
if (empty($invoice_id) || empty($company_code) || empty($branch_code) || empty($sales_rep_code) || empty($customer_phone)) {
    $error_message = "رابط غير صحيح. يرجى التأكد من الرابط المرسل إليك.";
}

// التحقق من وجود رد سابق لنفس الفاتورة
if (!isset($error_message)) {
    $stmt = $pdo->prepare("SELECT id FROM survey_responses WHERE invoice_id = ?");
    $stmt->execute([$invoice_id]);
    if ($stmt->fetch()) {
        $error_message = "تم الإجابة على هذا الاستبيان مسبقاً. شكراً لك!";
    }
}

// جلب إعدادات الاستبيان
$stmt = $pdo->prepare("SELECT * FROM survey_settings WHERE is_active = 1 LIMIT 1");
$stmt->execute();
$survey_settings = $stmt->fetch();

if (!$survey_settings) {
    $survey_settings = [
        'survey_title' => 'استبيان رضا العملاء',
        'survey_description' => 'شكراً لتسوقك معنا نحن متحمسون لسماع رأيك في تجربتك معنا هنا:',
        'thank_you_message' => 'شكراً لك على وقتك في الإجابة على الاستبيان. رأيك مهم جداً لنا ويساعدنا على تحسين خدماتنا.'
    ];
}

// تحديد الاستبيان (افتراضي = 1)
$survey_id = intval($_GET['survey_id'] ?? 1);

// جلب بيانات الاستبيان
$stmt = $pdo->prepare("SELECT * FROM surveys WHERE id = ? AND is_active = 1");
$stmt->execute([$survey_id]);
$survey = $stmt->fetch();

// إذا لم يوجد الاستبيان، استخدم الافتراضي
if (!$survey) {
    $survey_id = 1;
    $stmt = $pdo->prepare("SELECT * FROM surveys WHERE id = ? AND is_active = 1");
    $stmt->execute([$survey_id]);
    $survey = $stmt->fetch();
}

// إذا لم يوجد أي استبيان، إنشاء افتراضي
if (!$survey) {
    $survey = [
        'id' => 1,
        'name' => 'استبيان رضا العملاء',
        'description' => 'شكراً لتسوقك معنا نحن متحمسون لسماع رأيك في تجربتك معنا هنا',
        'theme_color' => '#667eea'
    ];
}

// جلب الأسئلة النشطة
$stmt = $pdo->prepare("SELECT * FROM survey_questions WHERE survey_id = ? AND is_active = 1 ORDER BY question_order");
$stmt->execute([$survey_id]);
$questions = $stmt->fetchAll();

// معالجة إرسال النموذج
if ($_SERVER['REQUEST_METHOD'] == 'POST' && !isset($error_message)) {
    try {
        $pdo->beginTransaction();
        
        // إدراج الرد الرئيسي
        $stmt = $pdo->prepare("
            INSERT INTO survey_responses (survey_id, invoice_id, company_code, branch_code, sales_rep_code, customer_phone, ip_address, user_agent)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([
            $survey_id,
            $invoice_id,
            $company_code,
            $branch_code,
            $sales_rep_code,
            $customer_phone,
            $_SERVER['REMOTE_ADDR'] ?? null,
            $_SERVER['HTTP_USER_AGENT'] ?? null
        ]);
        
        $response_id = $pdo->lastInsertId();
        
        // إدراج الإجابات
        foreach ($questions as $question) {
            $answer_key = 'question_' . $question['id'];
            
            if (isset($_POST[$answer_key])) {
                $answer_value = $_POST[$answer_key];
                
                // التحقق من الإجابات المطلوبة
                if ($question['is_required'] && empty($answer_value)) {
                    throw new Exception("يرجى الإجابة على جميع الأسئلة المطلوبة");
                }
                
                $rating_value = null;
                $text_value = null;
                
                if ($question['question_type'] == 'rating') {
                    $rating_value = intval($answer_value);
                    if ($rating_value < 1 || $rating_value > 5) {
                        throw new Exception("تقييم غير صحيح");
                    }
                } else {
                    $text_value = sanitizeInput($answer_value);
                }
                
                $stmt = $pdo->prepare("
                    INSERT INTO survey_answers (response_id, question_id, answer_value, rating_value) 
                    VALUES (?, ?, ?, ?)
                ");
                $stmt->execute([$response_id, $question['id'], $text_value, $rating_value]);
            }
        }
        
        $pdo->commit();
        $success_message = $survey_settings['thank_you_message'];
        
    } catch (Exception $e) {
        $pdo->rollBack();
        $error_message = $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($survey_settings['survey_title']); ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, <?php echo $survey['theme_color']; ?> 0%, <?php echo $survey['theme_color']; ?>dd 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        
        .survey-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .survey-header {
            background: linear-gradient(45deg, <?php echo $survey['theme_color']; ?>, <?php echo $survey['theme_color']; ?>dd);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }
        
        .survey-header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 15px;
        }
        
        .survey-body {
            padding: 40px 30px;
        }
        
        .question-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }
        
        .question-card:hover {
            border-color: <?php echo $survey['theme_color']; ?>;
            transform: translateY(-2px);
        }
        
        .question-text {
            font-size: 1.2rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
        }
        
        .rating-stars {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin: 20px 0;
        }
        
        .star {
            font-size: 2.5rem;
            color: #ddd;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .star:hover,
        .star.active {
            color: #ffc107;
            transform: scale(1.1);
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 15px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }
        
        .form-control:focus {
            border-color: <?php echo $survey['theme_color']; ?>;
            box-shadow: 0 0 0 0.2rem <?php echo $survey['theme_color']; ?>40;
        }
        
        .btn-submit {
            background: linear-gradient(45deg, <?php echo $survey['theme_color']; ?>, <?php echo $survey['theme_color']; ?>dd);
            border: none;
            border-radius: 50px;
            padding: 15px 40px;
            font-size: 1.2rem;
            font-weight: 600;
            color: white;
            transition: all 0.3s ease;
        }
        
        .btn-submit:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px <?php echo $survey['theme_color']; ?>50;
        }
        
        .alert {
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 25px;
        }
        
        .success-animation {
            text-align: center;
            padding: 40px;
        }
        
        .success-icon {
            font-size: 4rem;
            color: #28a745;
            margin-bottom: 20px;
            animation: bounce 1s ease-in-out;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }
        
        .required {
            color: #dc3545;
        }
        
        @media (max-width: 768px) {
            .survey-header h1 {
                font-size: 2rem;
            }
            
            .survey-body {
                padding: 20px 15px;
            }
            
            .question-card {
                padding: 20px 15px;
            }
            
            .star {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="survey-container">
            <div class="survey-header">
                <h1><?php echo htmlspecialchars($survey['name']); ?></h1>
                <p class="lead mb-0"><?php echo htmlspecialchars($survey['description'] ?? $survey_settings['survey_description']); ?></p>
            </div>
            
            <div class="survey-body">
                <?php if (isset($success_message)): ?>
                    <div class="success-animation">
                        <div class="success-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <h3 class="text-success mb-3">تم إرسال الاستبيان بنجاح!</h3>
                        <p class="lead"><?php echo htmlspecialchars($success_message); ?></p>
                    </div>
                <?php elseif (isset($error_message)): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php echo htmlspecialchars($error_message); ?>
                    </div>
                <?php else: ?>
                    <form method="POST" id="surveyForm">
                        <?php foreach ($questions as $index => $question): ?>
                            <div class="question-card">
                                <div class="question-text">
                                    <?php echo ($index + 1) . '. ' . htmlspecialchars($question['question_text']); ?>
                                    <?php if ($question['is_required']): ?>
                                        <span class="required">*</span>
                                    <?php endif; ?>
                                </div>
                                
                                <?php if ($question['question_type'] == 'rating'): ?>
                                    <div class="rating-stars" data-question="<?php echo $question['id']; ?>">
                                        <?php for ($i = 1; $i <= 5; $i++): ?>
                                            <i class="fas fa-star star" data-rating="<?php echo $i; ?>"></i>
                                        <?php endfor; ?>
                                    </div>
                                    <input type="hidden" name="question_<?php echo $question['id']; ?>" id="rating_<?php echo $question['id']; ?>" <?php echo $question['is_required'] ? 'required' : ''; ?>>
                                <?php else: ?>
                                    <textarea 
                                        class="form-control" 
                                        name="question_<?php echo $question['id']; ?>" 
                                        rows="4" 
                                        placeholder="اكتب إجابتك هنا..."
                                        <?php echo $question['is_required'] ? 'required' : ''; ?>
                                    ></textarea>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; ?>
                        
                        <div class="text-center mt-4">
                            <button type="submit" class="btn btn-submit">
                                <i class="fas fa-paper-plane me-2"></i>
                                إرسال الاستبيان
                            </button>
                        </div>
                    </form>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // تفعيل نظام التقييم بالنجوم
        document.querySelectorAll('.rating-stars').forEach(function(ratingContainer) {
            const stars = ratingContainer.querySelectorAll('.star');
            const questionId = ratingContainer.dataset.question;
            const hiddenInput = document.getElementById('rating_' + questionId);
            
            stars.forEach(function(star, index) {
                star.addEventListener('click', function() {
                    const rating = parseInt(star.dataset.rating);
                    hiddenInput.value = rating;
                    
                    // تحديث مظهر النجوم
                    stars.forEach(function(s, i) {
                        if (i < rating) {
                            s.classList.add('active');
                        } else {
                            s.classList.remove('active');
                        }
                    });
                });
                
                // تأثير الهوفر
                star.addEventListener('mouseenter', function() {
                    const rating = parseInt(star.dataset.rating);
                    stars.forEach(function(s, i) {
                        if (i < rating) {
                            s.style.color = '#ffc107';
                        } else {
                            s.style.color = '#ddd';
                        }
                    });
                });
            });
            
            // إعادة تعيين الألوان عند مغادرة المنطقة
            ratingContainer.addEventListener('mouseleave', function() {
                const currentRating = parseInt(hiddenInput.value) || 0;
                stars.forEach(function(s, i) {
                    if (i < currentRating) {
                        s.style.color = '#ffc107';
                    } else {
                        s.style.color = '#ddd';
                    }
                });
            });
        });
        
        // التحقق من النموذج قبل الإرسال
        document.getElementById('surveyForm')?.addEventListener('submit', function(e) {
            const requiredRatings = document.querySelectorAll('input[type="hidden"][required]');
            let isValid = true;
            
            requiredRatings.forEach(function(input) {
                if (!input.value) {
                    isValid = false;
                    const questionCard = input.closest('.question-card');
                    questionCard.style.borderColor = '#dc3545';
                    questionCard.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
            });
            
            if (!isValid) {
                e.preventDefault();
                alert('يرجى الإجابة على جميع الأسئلة المطلوبة');
            }
        });
    </script>
</body>
</html>
