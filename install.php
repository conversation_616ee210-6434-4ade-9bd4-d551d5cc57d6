<?php
/**
 * ملف تثبيت قاعدة البيانات
 * Database Installation Script
 */

// إعدادات قاعدة البيانات
$servername = "localhost";
$username_db = "root";
$password_db = "";
$dbname = "jst_survey";

// التحقق من وجود معالج التثبيت
$install_requested = isset($_GET['install']) && $_GET['install'] == '1';

if ($install_requested) {
    try {
        // الاتصال بـ MySQL بدون تحديد قاعدة بيانات
        $pdo = new PDO("mysql:host=$servername;charset=utf8mb4", $username_db, $password_db);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        echo "<h2>بدء تثبيت قاعدة البيانات...</h2>";

        // قراءة ملف SQL
        $sql = file_get_contents('database.sql');

        if ($sql === false) {
            throw new Exception("لا يمكن قراءة ملف database.sql");
        }

        // تقسيم الاستعلامات
        $statements = explode(';', $sql);

        $successCount = 0;
        $errorCount = 0;

        foreach ($statements as $statement) {
            $statement = trim($statement);

            // تجاهل التعليقات والأسطر الفارغة
            if (empty($statement) || strpos($statement, '--') === 0) {
                continue;
            }

            try {
                $pdo->exec($statement);
                $successCount++;
                echo "<p style='color: green;'>✓ تم تنفيذ الاستعلام بنجاح</p>";
            } catch (PDOException $e) {
                $errorCount++;
                echo "<p style='color: red;'>✗ خطأ في الاستعلام: " . $e->getMessage() . "</p>";
                echo "<pre style='background: #f5f5f5; padding: 10px; margin: 5px 0;'>" . htmlspecialchars($statement) . "</pre>";
            }
        }
    
    echo "<hr>";
    echo "<h3>نتائج التثبيت:</h3>";
    echo "<p><strong>الاستعلامات الناجحة:</strong> $successCount</p>";
    echo "<p><strong>الاستعلامات الفاشلة:</strong> $errorCount</p>";
    
    if ($errorCount == 0) {
        echo "<div style='background: #d4edda; color: #155724; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4>🎉 تم تثبيت قاعدة البيانات بنجاح!</h4>";
        echo "<p>يمكنك الآن استخدام النظام.</p>";
        echo "<p><a href='admin/login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>الذهاب إلى لوحة الإدارة</a></p>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4>⚠️ حدثت أخطاء أثناء التثبيت</h4>";
        echo "<p>يرجى مراجعة الأخطاء أعلاه وإصلاحها قبل المتابعة.</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>❌ خطأ في الاتصال بقاعدة البيانات</h4>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "<p>تأكد من:</p>";
    echo "<ul>";
    echo "<li>تشغيل خادم MySQL</li>";
    echo "<li>صحة بيانات الاتصال في ملف config.php</li>";
    echo "<li>وجود صلاحيات إنشاء قواعد البيانات</li>";
    echo "</ul>";
    echo "</div>";
}
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت نظام الاستبيان</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        
        h1, h2, h3 {
            color: #333;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        pre {
            font-size: 12px;
            overflow-x: auto;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            margin: 20px 0;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border: 1px solid #f5c6cb;
            border-radius: 5px;
            margin: 20px 0;
        }
        
        .btn {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            display: inline-block;
            margin: 10px 5px;
        }
        
        .btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 تثبيت نظام استبيان JST</h1>
        <p>مرحباً بك في معالج تثبيت نظام الاستبيان. سيقوم هذا المعالج بإنشاء قاعدة البيانات وإدراج البيانات الافتراضية.</p>
        
        <?php if (!$install_requested): ?>
        <div class="error">
            <h4>❌ لم يتم تشغيل التثبيت بعد</h4>
            <p>انقر على الزر أدناه لبدء عملية التثبيت:</p>
            <a href="?install=1" class="btn">بدء التثبيت</a>
        </div>
        <?php endif; ?>
        
        <hr>
        <h3>متطلبات النظام:</h3>
        <ul>
            <li>PHP 7.4 أو أحدث ✓</li>
            <li>MySQL 5.7 أو أحدث ✓</li>
            <li>امتداد PDO ✓</li>
            <li>امتداد OpenSSL ✓</li>
        </ul>
        
        <h3>الميزات المتضمنة:</h3>
        <ul>
            <li>نظام إدارة المستخدمين مع الصلاحيات</li>
            <li>إدارة الشركات والفروع والمندوبين</li>
            <li>نظام الاستبيان التفاعلي</li>
            <li>التقارير والإحصائيات</li>
            <li>النسخ الاحتياطي والاستعادة</li>
            <li>نظام الأمان المتقدم</li>
        </ul>
    </div>
</body>
</html>
