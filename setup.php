<?php
/**
 * معالج التثبيت المبسط
 * Simple Installation Handler
 */

// إعدادات قاعدة البيانات
$servername = "localhost";
$username_db = "root";
$password_db = "";
$dbname = "jst_survey";

$install_requested = isset($_GET['install']) && $_GET['install'] == '1';
$success = false;
$errors = [];

if ($install_requested) {
    try {
        // الاتصال بـ MySQL بدون تحديد قاعدة بيانات
        $pdo = new PDO("mysql:host=$servername;charset=utf8mb4", $username_db, $password_db);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // إنشاء قاعدة البيانات
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        $pdo->exec("USE `$dbname`");
        
        // قراءة ملف SQL
        if (!file_exists('database.sql')) {
            throw new Exception("ملف database.sql غير موجود");
        }
        
        $sql = file_get_contents('database.sql');
        if ($sql === false) {
            throw new Exception("لا يمكن قراءة ملف database.sql");
        }
        
        // تنفيذ الاستعلامات
        $statements = explode(';', $sql);
        $successCount = 0;
        
        foreach ($statements as $statement) {
            $statement = trim($statement);
            
            if (empty($statement) || strpos($statement, '--') === 0) {
                continue;
            }
            
            try {
                $pdo->exec($statement);
                $successCount++;
            } catch (PDOException $e) {
                $errors[] = "خطأ في الاستعلام: " . $e->getMessage();
            }
        }
        
        if (empty($errors)) {
            $success = true;
        }
        
    } catch (Exception $e) {
        $errors[] = "خطأ في الاتصال: " . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت نظام الاستبيان</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 800px;
            margin-top: 50px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h3 class="mb-0">🚀 تثبيت نظام استبيان JST</h3>
            </div>
            <div class="card-body">
                <?php if ($install_requested): ?>
                    <?php if ($success): ?>
                        <div class="alert alert-success">
                            <h4>🎉 تم تثبيت النظام بنجاح!</h4>
                            <p>تم إنشاء قاعدة البيانات وجميع الجداول المطلوبة.</p>
                            <hr>
                            <h5>بيانات تسجيل الدخول الافتراضية:</h5>
                            <ul>
                                <li><strong>اسم المستخدم:</strong> Admin</li>
                                <li><strong>كلمة المرور:</strong> Admin123321</li>
                            </ul>
                            <div class="mt-3">
                                <a href="admin/login.php" class="btn btn-primary">الذهاب إلى لوحة الإدارة</a>
                                <a href="index.php?inv=TEST123&le=01&br=0101&sm=ID0003&cp=0501234567" class="btn btn-success">تجربة الاستبيان</a>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-danger">
                            <h4>❌ حدثت أخطاء أثناء التثبيت</h4>
                            <ul>
                                <?php foreach ($errors as $error): ?>
                                    <li><?php echo htmlspecialchars($error); ?></li>
                                <?php endforeach; ?>
                            </ul>
                            <a href="?install=1" class="btn btn-warning">إعادة المحاولة</a>
                        </div>
                    <?php endif; ?>
                <?php else: ?>
                    <h4>مرحباً بك في معالج التثبيت</h4>
                    <p>سيقوم هذا المعالج بإنشاء قاعدة البيانات وإعداد النظام للاستخدام.</p>
                    
                    <div class="alert alert-info">
                        <h5>متطلبات النظام:</h5>
                        <ul class="mb-0">
                            <li>PHP 7.4 أو أحدث ✓</li>
                            <li>MySQL 5.7 أو أحدث ✓</li>
                            <li>امتداد PDO ✓</li>
                        </ul>
                    </div>
                    
                    <div class="alert alert-warning">
                        <h5>⚠️ تأكد من:</h5>
                        <ul class="mb-0">
                            <li>تشغيل خادم MySQL</li>
                            <li>وجود صلاحيات إنشاء قواعد البيانات</li>
                            <li>وجود ملف database.sql في نفس المجلد</li>
                        </ul>
                    </div>
                    
                    <div class="text-center">
                        <a href="?install=1" class="btn btn-primary btn-lg">بدء التثبيت</a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</body>
</html>
