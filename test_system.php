<?php
/**
 * اختبار شامل للنظام
 * System Comprehensive Test
 */

// إعدادات قاعدة البيانات
$servername = "localhost";
$username_db = "root";
$password_db = "";
$dbname = "jst_survey";

$tests = [];
$passed = 0;
$failed = 0;

// اختبار الاتصال بقاعدة البيانات
try {
    $pdo = new PDO("mysql:host=$servername;dbname=$dbname;charset=utf8mb4", $username_db, $password_db);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $tests[] = ['name' => 'الاتصال بقاعدة البيانات', 'status' => 'pass', 'message' => 'تم بنجاح'];
    $passed++;
} catch (Exception $e) {
    $tests[] = ['name' => 'الاتصال بقاعدة البيانات', 'status' => 'fail', 'message' => $e->getMessage()];
    $failed++;
}

// اختبار الجداول المطلوبة
$required_tables = [
    'users', 'companies', 'branches', 'sales_representatives',
    'survey_questions', 'survey_settings', 'survey_responses', 
    'survey_answers', 'activity_logs', 'user_sessions', 
    'login_attempts', 'blocked_ips'
];

foreach ($required_tables as $table) {
    try {
        $stmt = $pdo->query("SELECT COUNT(*) FROM `$table`");
        $count = $stmt->fetchColumn();
        $tests[] = ['name' => "جدول $table", 'status' => 'pass', 'message' => "$count سجل"];
        $passed++;
    } catch (Exception $e) {
        $tests[] = ['name' => "جدول $table", 'status' => 'fail', 'message' => 'غير موجود'];
        $failed++;
    }
}

// اختبار المستخدم الافتراضي
try {
    $stmt = $pdo->prepare("SELECT * FROM users WHERE username = 'Admin'");
    $stmt->execute();
    $admin = $stmt->fetch();
    
    if ($admin) {
        $tests[] = ['name' => 'المستخدم الافتراضي', 'status' => 'pass', 'message' => 'موجود ومفعل'];
        $passed++;
    } else {
        $tests[] = ['name' => 'المستخدم الافتراضي', 'status' => 'fail', 'message' => 'غير موجود'];
        $failed++;
    }
} catch (Exception $e) {
    $tests[] = ['name' => 'المستخدم الافتراضي', 'status' => 'fail', 'message' => $e->getMessage()];
    $failed++;
}

// اختبار الأسئلة الافتراضية
try {
    $stmt = $pdo->query("SELECT COUNT(*) FROM survey_questions");
    $questions_count = $stmt->fetchColumn();
    
    if ($questions_count >= 5) {
        $tests[] = ['name' => 'الأسئلة الافتراضية', 'status' => 'pass', 'message' => "$questions_count سؤال"];
        $passed++;
    } else {
        $tests[] = ['name' => 'الأسئلة الافتراضية', 'status' => 'fail', 'message' => 'عدد غير كافي'];
        $failed++;
    }
} catch (Exception $e) {
    $tests[] = ['name' => 'الأسئلة الافتراضية', 'status' => 'fail', 'message' => $e->getMessage()];
    $failed++;
}

// اختبار البيانات الافتراضية
try {
    $stmt = $pdo->query("SELECT COUNT(*) FROM companies");
    $companies = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM branches");
    $branches = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM sales_representatives");
    $reps = $stmt->fetchColumn();
    
    if ($companies > 0 && $branches > 0 && $reps > 0) {
        $tests[] = ['name' => 'البيانات الافتراضية', 'status' => 'pass', 'message' => "$companies شركة، $branches فرع، $reps مندوب"];
        $passed++;
    } else {
        $tests[] = ['name' => 'البيانات الافتراضية', 'status' => 'fail', 'message' => 'بيانات ناقصة'];
        $failed++;
    }
} catch (Exception $e) {
    $tests[] = ['name' => 'البيانات الافتراضية', 'status' => 'fail', 'message' => $e->getMessage()];
    $failed++;
}

// اختبار الملفات المطلوبة
$required_files = [
    'index.php' => 'صفحة الاستبيان الرئيسية',
    'config.php' => 'ملف الإعدادات',
    'admin/login.php' => 'صفحة تسجيل الدخول',
    'admin/index.php' => 'لوحة المعلومات',
    'admin/reports.php' => 'صفحة التقارير',
    'includes/security.php' => 'ملف الأمان',
    '.htaccess' => 'ملف الحماية'
];

foreach ($required_files as $file => $description) {
    if (file_exists($file)) {
        $tests[] = ['name' => $description, 'status' => 'pass', 'message' => 'موجود'];
        $passed++;
    } else {
        $tests[] = ['name' => $description, 'status' => 'fail', 'message' => 'غير موجود'];
        $failed++;
    }
}

// اختبار المجلدات المطلوبة
$required_dirs = [
    'admin' => 'مجلد لوحة الإدارة',
    'includes' => 'مجلد الملفات المساعدة',
    'logs' => 'مجلد السجلات',
    'backups' => 'مجلد النسخ الاحتياطية'
];

foreach ($required_dirs as $dir => $description) {
    if (is_dir($dir)) {
        $tests[] = ['name' => $description, 'status' => 'pass', 'message' => 'موجود'];
        $passed++;
    } else {
        $tests[] = ['name' => $description, 'status' => 'fail', 'message' => 'غير موجود'];
        $failed++;
    }
}

$total = $passed + $failed;
$success_rate = $total > 0 ? round(($passed / $total) * 100, 1) : 0;
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النظام - نظام استبيان JST</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .test-pass { color: #28a745; }
        .test-fail { color: #dc3545; }
        .progress-bar-success { background-color: #28a745; }
        .progress-bar-danger { background-color: #dc3545; }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h3 class="mb-0">
                            <i class="fas fa-check-circle me-2"></i>
                            اختبار شامل لنظام استبيان JST
                        </h3>
                    </div>
                    <div class="card-body">
                        <!-- نتائج الاختبار -->
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <div class="card bg-success text-white">
                                    <div class="card-body text-center">
                                        <h4><?php echo $passed; ?></h4>
                                        <p class="mb-0">اختبار ناجح</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card bg-danger text-white">
                                    <div class="card-body text-center">
                                        <h4><?php echo $failed; ?></h4>
                                        <p class="mb-0">اختبار فاشل</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card bg-info text-white">
                                    <div class="card-body text-center">
                                        <h4><?php echo $success_rate; ?>%</h4>
                                        <p class="mb-0">معدل النجاح</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- شريط التقدم -->
                        <div class="progress mb-4" style="height: 25px;">
                            <div class="progress-bar progress-bar-success" style="width: <?php echo $success_rate; ?>%">
                                <?php echo $success_rate; ?>% نجح
                            </div>
                        </div>

                        <!-- تفاصيل الاختبارات -->
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>الاختبار</th>
                                        <th>الحالة</th>
                                        <th>التفاصيل</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($tests as $test): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($test['name']); ?></td>
                                        <td>
                                            <?php if ($test['status'] == 'pass'): ?>
                                                <span class="test-pass">
                                                    <i class="fas fa-check-circle"></i> نجح
                                                </span>
                                            <?php else: ?>
                                                <span class="test-fail">
                                                    <i class="fas fa-times-circle"></i> فشل
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo htmlspecialchars($test['message']); ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- الخلاصة والروابط -->
                        <div class="mt-4">
                            <?php if ($success_rate >= 90): ?>
                                <div class="alert alert-success">
                                    <h4><i class="fas fa-check-circle"></i> النظام جاهز للاستخدام!</h4>
                                    <p>تم اجتياز معظم الاختبارات بنجاح. يمكنك الآن استخدام النظام.</p>
                                    <hr>
                                    <div class="d-flex gap-2">
                                        <a href="admin/login.php" class="btn btn-primary">
                                            <i class="fas fa-sign-in-alt"></i> لوحة الإدارة
                                        </a>
                                        <a href="index.php?inv=TEST123&le=01&br=0101&sm=ID0003&cp=0501234567" class="btn btn-success">
                                            <i class="fas fa-poll"></i> تجربة الاستبيان
                                        </a>
                                    </div>
                                </div>
                            <?php else: ?>
                                <div class="alert alert-warning">
                                    <h4><i class="fas fa-exclamation-triangle"></i> يحتاج النظام لإصلاحات</h4>
                                    <p>هناك بعض المشاكل التي تحتاج لحل قبل استخدام النظام.</p>
                                    <a href="setup.php" class="btn btn-warning">إعادة التثبيت</a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
