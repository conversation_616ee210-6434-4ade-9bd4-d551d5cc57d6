<?php
/**
 * تحديث قاعدة البيانات لدعم الاستبيانات المتعددة
 * Update Database for Multiple Surveys Support
 */

require_once 'config.php';

$success_message = '';
$error_message = '';
$updates_applied = [];

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['update_database'])) {
    try {
        $pdo->beginTransaction();
        
        // 1. إنشاء جدول الاستبيانات إذا لم يكن موجوداً
        $stmt = $pdo->query("SHOW TABLES LIKE 'surveys'");
        if (!$stmt->fetch()) {
            $pdo->exec("
                CREATE TABLE `surveys` (
                  `id` int(11) NOT NULL AUTO_INCREMENT,
                  `name` varchar(255) NOT NULL COMMENT 'اسم الاستبيان للإدارة فقط',
                  `title` varchar(255) NOT NULL COMMENT 'عنوان الاستبيان للعميل H1',
                  `description` text COMMENT 'وصف الاستبيان للعميل P',
                  `thank_you_message` text COMMENT 'رسالة الشكر بعد الإرسال',
                  `theme_color` varchar(7) NOT NULL DEFAULT '#667eea',
                  `is_active` tinyint(1) NOT NULL DEFAULT 1,
                  `created_by` int(11) NOT NULL,
                  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                  PRIMARY KEY (`id`),
                  KEY `created_by` (`created_by`),
                  KEY `is_active` (`is_active`),
                  FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
            $updates_applied[] = 'تم إنشاء جدول الاستبيانات (surveys)';
        }

        // 1.1. إضافة الحقول الجديدة إذا لم تكن موجودة
        $stmt = $pdo->query("SHOW COLUMNS FROM surveys LIKE 'title'");
        if (!$stmt->fetch()) {
            $pdo->exec("ALTER TABLE surveys ADD COLUMN title varchar(255) NOT NULL DEFAULT 'استبيان رضا العملاء' AFTER name");
            $updates_applied[] = 'تم إضافة حقل العنوان (title) لجدول الاستبيانات';
        }

        $stmt = $pdo->query("SHOW COLUMNS FROM surveys LIKE 'thank_you_message'");
        if (!$stmt->fetch()) {
            $pdo->exec("ALTER TABLE surveys ADD COLUMN thank_you_message text AFTER description");
            $updates_applied[] = 'تم إضافة حقل رسالة الشكر (thank_you_message) لجدول الاستبيانات';
        }
        
        // 2. إضافة عمود survey_id إلى جدول survey_questions إذا لم يكن موجوداً
        $stmt = $pdo->query("SHOW COLUMNS FROM survey_questions LIKE 'survey_id'");
        if (!$stmt->fetch()) {
            $pdo->exec("ALTER TABLE survey_questions ADD COLUMN survey_id int(11) NOT NULL DEFAULT 1 AFTER id");
            $pdo->exec("ALTER TABLE survey_questions ADD KEY survey_id (survey_id)");
            $pdo->exec("ALTER TABLE survey_questions ADD FOREIGN KEY (survey_id) REFERENCES surveys(id) ON DELETE CASCADE");
            $updates_applied[] = 'تم إضافة عمود survey_id إلى جدول الأسئلة';
        }
        
        // 3. إضافة عمود survey_id إلى جدول survey_responses إذا لم يكن موجوداً
        $stmt = $pdo->query("SHOW COLUMNS FROM survey_responses LIKE 'survey_id'");
        if (!$stmt->fetch()) {
            $pdo->exec("ALTER TABLE survey_responses ADD COLUMN survey_id int(11) NOT NULL DEFAULT 1 AFTER id");
            $pdo->exec("ALTER TABLE survey_responses ADD KEY survey_id (survey_id)");
            $pdo->exec("ALTER TABLE survey_responses ADD FOREIGN KEY (survey_id) REFERENCES surveys(id) ON DELETE CASCADE");
            $updates_applied[] = 'تم إضافة عمود survey_id إلى جدول الردود';
        }
        
        // 4. إدراج الاستبيان الافتراضي إذا لم يكن موجوداً
        $stmt = $pdo->query("SELECT COUNT(*) FROM surveys");
        if ($stmt->fetchColumn() == 0) {
            // البحث عن أول مستخدم مدير
            $stmt = $pdo->query("SELECT id FROM users WHERE role IN ('super_admin', 'manager') ORDER BY id LIMIT 1");
            $admin_id = $stmt->fetchColumn();
            
            if ($admin_id) {
                $pdo->prepare("
                    INSERT INTO surveys (id, name, title, description, thank_you_message, theme_color, is_active, created_by)
                    VALUES (1, 'استبيان رضا العملاء الافتراضي', 'استبيان رضا العملاء', 'شكراً لتسوقك معنا نحن متحمسون لسماع رأيك في تجربتك معنا هنا', 'شكراً لك على وقتك في الإجابة على الاستبيان. رأيك مهم جداً لنا ويساعدنا على تحسين خدماتنا.', '#667eea', 1, ?)
                ")->execute([$admin_id]);
                $updates_applied[] = 'تم إدراج الاستبيان الافتراضي';
            }
        }
        
        // 5. تحديث الأسئلة الموجودة لتنتمي للاستبيان الافتراضي
        $stmt = $pdo->query("SELECT COUNT(*) FROM survey_questions WHERE survey_id = 1");
        if ($stmt->fetchColumn() == 0) {
            $pdo->exec("UPDATE survey_questions SET survey_id = 1 WHERE survey_id = 0 OR survey_id IS NULL");
            $updates_applied[] = 'تم ربط الأسئلة الموجودة بالاستبيان الافتراضي';
        }
        
        // 6. تحديث الردود الموجودة لتنتمي للاستبيان الافتراضي
        $stmt = $pdo->query("SELECT COUNT(*) FROM survey_responses WHERE survey_id = 1");
        $total_responses = $pdo->query("SELECT COUNT(*) FROM survey_responses")->fetchColumn();
        if ($stmt->fetchColumn() < $total_responses) {
            $pdo->exec("UPDATE survey_responses SET survey_id = 1 WHERE survey_id = 0 OR survey_id IS NULL");
            $updates_applied[] = 'تم ربط الردود الموجودة بالاستبيان الافتراضي';
        }
        
        $pdo->commit();
        
        if (empty($updates_applied)) {
            $success_message = 'قاعدة البيانات محدثة بالفعل - لا توجد تحديثات مطلوبة';
        } else {
            $success_message = 'تم تحديث قاعدة البيانات بنجاح!';
        }
        
    } catch (Exception $e) {
        $pdo->rollBack();
        $error_message = 'حدث خطأ: ' . $e->getMessage();
    }
}

// فحص حالة قاعدة البيانات
$database_status = [];

// فحص جدول الاستبيانات
$stmt = $pdo->query("SHOW TABLES LIKE 'surveys'");
$database_status['surveys_table'] = $stmt->fetch() ? true : false;

// فحص عمود survey_id في جدول الأسئلة
$stmt = $pdo->query("SHOW COLUMNS FROM survey_questions LIKE 'survey_id'");
$database_status['questions_survey_id'] = $stmt->fetch() ? true : false;

// فحص عمود survey_id في جدول الردود
$stmt = $pdo->query("SHOW COLUMNS FROM survey_responses LIKE 'survey_id'");
$database_status['responses_survey_id'] = $stmt->fetch() ? true : false;

// عدد الاستبيانات
if ($database_status['surveys_table']) {
    $stmt = $pdo->query("SELECT COUNT(*) FROM surveys");
    $database_status['surveys_count'] = $stmt->fetchColumn();
} else {
    $database_status['surveys_count'] = 0;
}

$needs_update = !$database_status['surveys_table'] || 
                !$database_status['questions_survey_id'] || 
                !$database_status['responses_survey_id'] ||
                $database_status['surveys_count'] == 0;
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحديث قاعدة البيانات - نظام الاستبيان</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        
        .container {
            max-width: 900px;
        }
        
        .card {
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            border: none;
        }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            padding: 20px;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            margin: 10px 0;
            border-radius: 10px;
            background: #f8f9fa;
        }
        
        .status-ok {
            background: #d4edda;
            border-left: 4px solid #28a745;
        }
        
        .status-error {
            background: #f8d7da;
            border-left: 4px solid #dc3545;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <div class="card-header text-center">
                <h2><i class="fas fa-database me-3"></i>تحديث قاعدة البيانات</h2>
                <p class="mb-0">دعم الاستبيانات المتعددة</p>
            </div>
            
            <div class="card-body">
                <?php if ($success_message): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo htmlspecialchars($success_message); ?>
                        
                        <?php if (!empty($updates_applied)): ?>
                            <hr>
                            <h6>التحديثات المطبقة:</h6>
                            <ul class="mb-0">
                                <?php foreach ($updates_applied as $update): ?>
                                    <li><?php echo htmlspecialchars($update); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        <?php endif; ?>
                        
                        <hr>
                        <a href="admin/surveys.php" class="btn btn-success">
                            <i class="fas fa-poll me-2"></i>
                            الذهاب إلى إدارة الاستبيانات
                        </a>
                    </div>
                <?php endif; ?>
                
                <?php if ($error_message): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php echo htmlspecialchars($error_message); ?>
                    </div>
                <?php endif; ?>
                
                <h5><i class="fas fa-clipboard-check me-2"></i>حالة قاعدة البيانات</h5>
                
                <div class="status-item <?php echo $database_status['surveys_table'] ? 'status-ok' : 'status-error'; ?>">
                    <span>جدول الاستبيانات (surveys)</span>
                    <span>
                        <?php if ($database_status['surveys_table']): ?>
                            <i class="fas fa-check-circle text-success"></i> موجود
                        <?php else: ?>
                            <i class="fas fa-times-circle text-danger"></i> غير موجود
                        <?php endif; ?>
                    </span>
                </div>
                
                <div class="status-item <?php echo $database_status['questions_survey_id'] ? 'status-ok' : 'status-error'; ?>">
                    <span>عمود survey_id في جدول الأسئلة</span>
                    <span>
                        <?php if ($database_status['questions_survey_id']): ?>
                            <i class="fas fa-check-circle text-success"></i> موجود
                        <?php else: ?>
                            <i class="fas fa-times-circle text-danger"></i> غير موجود
                        <?php endif; ?>
                    </span>
                </div>
                
                <div class="status-item <?php echo $database_status['responses_survey_id'] ? 'status-ok' : 'status-error'; ?>">
                    <span>عمود survey_id في جدول الردود</span>
                    <span>
                        <?php if ($database_status['responses_survey_id']): ?>
                            <i class="fas fa-check-circle text-success"></i> موجود
                        <?php else: ?>
                            <i class="fas fa-times-circle text-danger"></i> غير موجود
                        <?php endif; ?>
                    </span>
                </div>
                
                <div class="status-item <?php echo $database_status['surveys_count'] > 0 ? 'status-ok' : 'status-error'; ?>">
                    <span>عدد الاستبيانات</span>
                    <span>
                        <?php if ($database_status['surveys_count'] > 0): ?>
                            <i class="fas fa-check-circle text-success"></i> <?php echo $database_status['surveys_count']; ?> استبيان
                        <?php else: ?>
                            <i class="fas fa-times-circle text-danger"></i> لا توجد استبيانات
                        <?php endif; ?>
                    </span>
                </div>
                
                <?php if ($needs_update): ?>
                    <div class="alert alert-warning mt-4">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تحديث مطلوب:</strong> قاعدة البيانات تحتاج إلى تحديث لدعم الاستبيانات المتعددة.
                    </div>
                    
                    <form method="POST" class="text-center">
                        <button type="submit" name="update_database" class="btn btn-primary btn-lg"
                                onclick="return confirm('هل تريد تحديث قاعدة البيانات؟')">
                            <i class="fas fa-sync-alt me-2"></i>
                            تحديث قاعدة البيانات
                        </button>
                    </form>
                <?php else: ?>
                    <div class="alert alert-success mt-4">
                        <i class="fas fa-check-circle me-2"></i>
                        <strong>قاعدة البيانات محدثة:</strong> جميع التحديثات مطبقة بنجاح.
                    </div>
                    
                    <div class="text-center">
                        <a href="admin/surveys.php" class="btn btn-primary">
                            <i class="fas fa-poll me-2"></i>
                            الذهاب إلى إدارة الاستبيانات
                        </a>
                    </div>
                <?php endif; ?>
                
                <div class="text-center mt-3">
                    <a href="admin/index.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة إلى لوحة الإدارة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
